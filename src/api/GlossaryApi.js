import request, {download} from "@/utils/request";

const baseUrl = import.meta.env.VITE_APP_BASE_API


export function saveGlossarySceneShare(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossaryScene/saveShare',
        data: params || {}
    })
}

export function getGlossary(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossary/list',
        data: params || {}
    })
}

export function getGlossaryByIds(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossary/listBySceneIds',
        data: params || {}
    })
}

export function addGlossary(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossary/save',
        data: params || {}
    })
}
// 批量新增词汇
export function batchAddGlossary(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossary/batchAdd',
        data: params || {}
    })
}
export function deleteGlossary(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossary/delete',
        data: params || {}
    })
}
export function downGlossary(data) {
    return download("/conf/asr/glossary/export", data,"Vocabulary.xls")
}

// 词汇场景接口
export function getGlossaryScene(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossaryScene/list',
        data: params || {}
    })
}
export function getAllGlossaryScene(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossaryScene/listAll',
        data: params || {}
    })
}
export function addGlossaryScene(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/glossaryScene/save',
        data: params || {}
    })
}
export function deleteGlossaryScene(sceneId) {
    return request({
        method: 'GET',
        url: `/conf/asr/glossaryScene/deleteById/${sceneId}`,
    })
}
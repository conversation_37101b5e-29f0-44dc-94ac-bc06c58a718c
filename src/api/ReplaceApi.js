import request, {download} from "@/utils/request";

const baseUrl = import.meta.env.VITE_APP_BASE_API
export function getReplace(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/replaces/list',
        data: params || {}
    })
}
export function addReplace(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/replaces/save',
        data: params || {}
    })
}
// 批量新增替换
export function batchAddReplace(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/replaces/batchAdd',
        data: params || {}
    })
}
export function deleteReplace(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/replaces/delete',
        data: params || {}
    })
}
export function downReplace(data) {
    return download("/conf/asr/replaces/export", data,"ForceReplace.xls")
}
// 词汇场景接口
export function getReplaceScene(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/replaceScene/list',
        data: params || {}
    })
}
export function getAllReplaceScene(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/replaceScene/listAll',
        data: params || {}
    })
}
export function addReplaceScene(params) {
    return request({
        method: 'POST',
        url: '/conf/asr/replaceScene/save',
        data: params || {}
    })
}
export function deleteReplaceScene(sceneId) {
    return request({
        method: 'GET',
        url: `/conf/asr/replaceScene/deleteById/${sceneId}`,
    })
}
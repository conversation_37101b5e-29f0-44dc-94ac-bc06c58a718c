import request from "@/utils/request";

const baseUrl = import.meta.env.VITE_APP_BASE_API
export function login(params){
    params.type = 1
    return request({
        method: 'POST',
        url: '/conf/user/login',
        data: params || {}
    })
}

export function checkTerpMetaTrial(params){
    return request({
        method: 'POST',
        url: '/conf/user/checkTerpMeta',
        data: params || {}
    })
}
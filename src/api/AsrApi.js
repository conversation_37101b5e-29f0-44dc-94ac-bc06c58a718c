import request from "@/utils/request";

//讯飞asr
export function getAsrWssAdress(){
    if(process.env.NODE_ENV==='development'){
        return request({
            method: 'post',
            url: '/xfyunAsr'
        })
    }else{
        return request({
            method: 'post',
            url: 'http://117.72.99.27:15005/getAsrToken'
        })
    }
}


//azure asr
export function getAzureAsrToken(){
    if(process.env.NODE_ENV==='development'){
        return request({
            method: 'post',
            url: '/azureAsrToken'
        })
    }else{
        return request({
            method: 'post',
            url: 'http://117.72.99.27:15005/getAzureToken'
        })
    }
}

import OpenAI from "openai";

//deepseek 大模型
const openai = new OpenAI({
  baseURL: 'https://api.deepseek.com', 
  apiKey: '***********************************',
  dangerouslyAllowBrowser: true
});

export async function askQuestion(userMessage) {
  try {
    const completion = await openai.chat.completions.create({
      messages: [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: userMessage },
        
      ],
      stream: false,
      model: "deepseek-chat",
    });

    // 返回 AI 生成的回复
    return completion.choices[0].message.content;
  } catch (error) {
    console.error("Error calling OpenAI API:", error);
    throw new Error("Failed to get a response from the AI.");
  }
}
import request from "@/utils/request";
export function pdf2word(pdfFile,docxFile) {
    if(process.env.NODE_ENV==='development'){
        return request({
            method: 'post',
            url: '/convert',
            data: {
                "pdf_file": pdfFile,
                "docx_file": docxFile
            }
        })
    }else{
        return request({
            method: 'post',
            url: 'http://127.0.0.1:50055/convert',
            data: {
                "pdf_file": pdfFile,
                "docx_file": docxFile
            }
        })
    }
    
}

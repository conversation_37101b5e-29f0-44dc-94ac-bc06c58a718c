const glossary = {
    state () {
        return {
            glossaryVisible: false,
            glossarySceneId: null,
        }
    },
    mutations: {
        setGlossaryVisible(state, res) {
            state.glossaryVisible = res;
        },
        setGlossarySceneId(state, res) {
            state.glossarySceneId = res
        }
    },
    getters: {
        getGlossaryVisible(state) {
            return state.glossaryVisible
        },
        getGlossarySceneId(state) {
            return state.glossarySceneId
        }
    }
}

export default glossary
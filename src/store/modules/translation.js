const translation = {
    state () {
        return {
            translationRes: [],
            glossaryRes: [],
            replaceRes: [],
            searchValue: ''
        }
    },
    mutations: {
        setTranslationRes(state, res) {
            state.translationRes = res;
        },
        setGlossaryRes(state, res) {
            state.glossaryRes = res;
        },
        setReplaceRes(state, res) {
            state.replaceRes = res;
        },
        setSearchValue(state, res) {
            state.searchValue = res;
        }
    },
    getters: {
        getTranslationRes(state) {
           return state.translationRes
        },
        getGlossaryRes(state) {
            return state.glossaryRes
        },
        getReplaceRes(state) {
            return state.replaceRes
        },
        getSearchValue(state) {
            return state.searchValue
        },
    }
}

export default translation
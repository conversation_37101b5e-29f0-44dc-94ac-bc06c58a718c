const setting = {
    state () {
        return {
            settingVisible: false,
            color: '#e03024',
            fontSize: 28,
            theme: false,
            langList: []
        }
    },
    mutations: {
        setSettingVisible(state, res) {
            state.settingVisible = res;
        },
        setColor(state, res) {
            state.color = res
        },
        setFontSize(state, res) {
            state.fontSize = res
        },
        setTheme(state, res) {
            state.theme = res
        },
        setLangList(state, res) {
            state.langList = res
        }
    },
    getters: {
        getSettingVisible(state) {
            return state.settingVisible
        },
        getColor(state) {
            return state.color
        },
        getFontSize(state) {
            return state.fontSize
        },
        getTheme(state) {
            return state.theme
        },
        getLangList(state) {
            return state.langList
        }
    }
}

export default setting
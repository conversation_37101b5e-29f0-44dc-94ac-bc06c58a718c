const replace = {
    state () {
        return {
            replaceVisible: false,
            replaceSceneId: null,
        }
    },
    mutations: {
        setReplaceVisible(state, res) {
            state.replaceVisible = res;
        },
        setReplaceSceneId(state, res) {
            state.glossarySceneId = res
        }
    },
    getters: {
        getReplaceVisible(state) {
            return state.replaceVisible
        },
        getReplaceSceneId(state) {
            return state.glossarySceneId
        }
    }
}

export default replace
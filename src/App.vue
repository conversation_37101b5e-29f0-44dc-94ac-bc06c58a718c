<template>
    <keep-alive>
        <router-view />
    </keep-alive>
    <el-dialog v-model="alert" title="Progress">
      <div>
          <el-progress :percentage="progress"/>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="alert = false">OK</el-button>
        </div>
      </template>
    </el-dialog>
</template>

<script setup>
import { onMounted, ref } from "vue";
const { ipcRenderer } = require('electron')
import { ElMessageBox } from 'element-plus';

defineOptions({
  name: 'App'
});
const alert = ref(false)
const progress = ref(0)
onMounted(()=>{
  ipcRenderer.on("message", (event, message) => {
      console.log('message ',message);
      let index = message.index
      if(index == 2){
        ElMessageBox.confirm('Update availale. Click "OK" to continue.','Confirm',{
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          type: 'warning',
        }).then(() => {
          ipcRenderer.send('startUpdate');
        }).catch(() => {
          // 取消操作，可按需添加更多逻辑
        });
      }else if(index == 3){
        //暂无更新
          ElMessageBox.alert('There are currently no updates available.','Alert',{confirmButtonText: 'OK',});
      }
    });
    ipcRenderer.on("downloadProgress", (event, progressObj) => {
      alert.value = true
      progress.value =  Math.round(progressObj.percent);
      console.log('downloadProgress ',progress.value);
    });
    ipcRenderer.on("isUpdateNow", () => {
      console.log('isUpdateNow')
      alert.value = false
      ElMessageBox.confirm('Click "OK" to update now, or "CANCEL" at the next time when start up.','Confirm',{
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      type: 'warning',
    }).then(() => {
      ipcRenderer.send('isUpdateNow');
    }).catch(() => {
      // 取消操作，可按需添加更多逻辑
    });
    });
})
</script>
<style scoped>
.process{
  width: 350px;
}
</style>

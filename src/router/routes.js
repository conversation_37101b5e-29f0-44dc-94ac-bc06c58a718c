const routes = [
  {
    path: '',
    component: () => import('layouts/MainLayout.vue'),
    redirect: "/login",
    children: [
      { path: '/login', component: () => import('pages/login.vue') },
      // { path: '/interpretease', component: () => import('pages/footer.vue') },
    ]
  },
  {
    path: '/interpretease',
    name: 'interpretease',
    component: ()=>import('pages/footer.vue')
  },
  {
    path: '/fileTranslate',
    name: 'fileTranslate',
    component: ()=>import('pages/fileTranslate.vue')
  },
  {
    path: '/realtimeTranslate',
    name: 'realtimeTranslate',
    component: ()=>import('pages/realtimeTranslate.vue')
  },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue')
  }
]

export default routes

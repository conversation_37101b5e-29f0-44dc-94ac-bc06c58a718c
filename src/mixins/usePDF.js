import {onMounted, reactive} from "vue";
import {askQuestion} from "@/api/LLM";

export function usePDF() {

    const data = reactive({
        zoom: 0,
    })

    onMounted(() => {
        // 动态获取打包后的路径
        const workerPath = process.env.NODE_ENV === 'production'
          ? new URL('pdf/pdf.worker.mjs', window.location).toString()
          : new URL('public/pdf/pdf.worker.mjs', window.location).toString();
      
        pdfjsLib.GlobalWorkerOptions.workerSrc = workerPath;
      });


    const calculateHeight = (top1, top2) => {
        const matchTop1 = top1.match(/[\d.]+/)[0];
        const matchTop2 = top2.match(/[\d.]+/)[0];
        return Math.abs(matchTop1 - matchTop2) < 1;
    }

    const getMinFontSize = (arr) => {
        let minFontSize = Number(arr[0].style.fontSize.match(/[\d.]+/)[0])
        let index = 0
        for (let i = 0; i < arr.length; i++) {
            const item = arr[i]
            if(Number(item.style.fontSize.match(/[\d.]+/)[0]) < minFontSize) {
                minFontSize = Number(item.style.fontSize.match(/[\d.]+/)[0])
                index = i
            }
        }
        return index
    }

    const readPDF = async (path) => {
        const {ipcRenderer} = require('electron')
        const fileData = await ipcRenderer.invoke('read-file', path, null, {
            inWrapper: false
        });

        const pdf = await pdfjsLib.getDocument({
            data: fileData,
            cMapUrl: 'public/cmaps/',
            cMapPacked: true
        }).promise;
        const pdfWrapper = document.createElement('div');
        let pdfWrapperNew = document.createElement('div');
        pdfWrapper.classList.add('pdf-wrapper')
        for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);

            if (i === 1) {
                pdfWrapper.style.transform = `scale(${getScale(page)})`
                pdfWrapper.style.transformOrigin = 'top left'
                pdfWrapperNew = pdfWrapper.cloneNode()
            }

            // 获取默认的视图，来计算比例
            const defaultViewport = page.getViewport()
            const viewportParams = {
                scale: 1,
                rotation: getRotation(defaultViewport.rotation),
            }
            const viewport = page.getViewport(viewportParams)

            const canvas = setupCanvas(viewport)
            const outputScale = window.devicePixelRatio || 1
            const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : undefined
            const renderContext = {
                canvasContext: canvas.getContext('2d'),
                viewport,
                transform,
            }
            page.render(renderContext)
            pdfWrapper.appendChild(canvas)
        }
        document.getElementById('fileContent').appendChild(pdfWrapper)

        for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            // 获取默认的视图，来计算比例
            const defaultViewport = page.getViewport()
            const viewportParams = {
                scale: 1,
                rotation: getRotation(defaultViewport.rotation),
            }
            const viewport = page.getViewport(viewportParams)

            const textStream = page?.streamTextContent({includeMarkedContent: true, disableNormalization: true})
            const textLayer = new pdfjsLib.TextLayer({
                container: document.createElement('div'),
                textContentSource: textStream,
                viewport: viewport
            })
            let text = ''
            await textLayer.render().then(() => {
                for (const span of textLayer.textDivs) {
                    text = text + span.innerText
                }
            })
            const pageContainer = document.createElement('div')

            pageContainer.style.width = `${Math.floor(viewport.width)}px`
            pageContainer.style.height = `${Math.floor(viewport.height)}px`
            pageContainer.style.backgroundColor = '#FFFFFF'
            pageContainer.style.marginBottom = '5px'
            pageContainer.style.display = 'flex'
            pageContainer.style.flexDirection = 'column'
            pageContainer.style.justifyContent = 'center'
            pageContainer.style.alignItems = 'center'
            pageContainer.style.padding = '5px'
            pageContainer.style.overflowY = 'hidden'

            const p = document.createElement('p')
            p.innerText = text
            p.style.margin = '0'
            p.style.fontSize = '18px'
            p.style.maxWidth = '100%'
            p.style.maxHeight = '100%'
            p.style.overflowY = 'auto'

            pageContainer.appendChild(p)
            pdfWrapperNew.appendChild(pageContainer)
        }
        document.getElementById('translateContent').appendChild(pdfWrapperNew)
    }

    const setupCanvas = (viewport) => {
        let canvas
        canvas = document.createElement('canvas')
        canvas.style.display = 'block'
        canvas.setAttribute('dir', 'ltr')

        const outputScale = window.devicePixelRatio || 1
        canvas.width = Math.floor(viewport.width * outputScale)
        canvas.height = Math.floor(viewport.height * outputScale)

        canvas.style.width = `${Math.floor(viewport.width)}px`
        canvas.style.height = `${Math.floor(viewport.height)}px`
        canvas.style.marginBottom = '5px'

        return canvas
    }

    const getScale = (page) => {
        const parentWidth = document.getElementById('fileContent').clientWidth
        const scale1Width = page.getViewport({ scale: 1 }).width
        data.zoom = parentWidth / scale1Width
        return parentWidth / scale1Width
    }

    const resetScale = (type) => {
        if (type === 'in') {
            data.zoom += 0.1
        } else {
            data.zoom -= 0.1
        }
        document.querySelector('.file-content .pdf-wrapper').style.transform = `scale(${data.zoom})`
        document.querySelector('.translate-content .pdf-wrapper').style.transform = `scale(${data.zoom})`
    }

    const getRotation = (rotation) => {
        if (!(typeof rotation === 'number' && rotation % 90 === 0))
            return 0
        const factor = rotation / 90
        if (factor > 4)
            return getRotation(rotation - 360)
        else if (factor < 0)
            return getRotation(rotation + 360)
        return rotation
    }

    const getTopNumber = (span) => {
        try {
            const topBFH = span.style.top // 24.35%
            return (topBFH.slice(0, -1) / 100).toFixed(4)
        } catch (e) {
            console.log(e, span)
        }
    }
    const initDiv = (div, prevSpan, prevSpanHeight, scale, pageHeight) => {
        if (div.spanArr.length === 1) {
            div.width = prevSpan.getBoundingClientRect().width / scale
            div.height = prevSpan.getBoundingClientRect().height / scale
            div.lineHeight = prevSpan.style.height / scale
        } else {
            const widths = div.spanArr.map(span => span.getBoundingClientRect().width);
            div.width = Math.max(...widths) / scale
            div.lineHeight = (div.spanArr[1].getBoundingClientRect().top - div.spanArr[0].getBoundingClientRect().top) / scale
            div.height = (div.spanArr[div.spanArr.length - 1].getBoundingClientRect().top - div.spanArr[0].getBoundingClientRect().top + prevSpanHeight) / scale
        }
        return div
    }
    // 分段处理
    const handleParagraphByHeight = () => {
        const pageContainerArr = document.querySelectorAll('.pdf-wrapper .textLayer')
        for (const page of pageContainerArr) {
            // 根据和上一个 span 的高度与自己高度比较 分段
            const spanList = page.querySelectorAll('span')
            if (spanList.length === 0) {
                continue
            }
            const divList = []
            let div = {
                spanArr: [spanList[0]],
                width: 0,
                height: 0,
                lineHeight: 0,
                top: spanList[0].style.top,
                left: spanList[0].style.left
            }
            for (let j = 1; j < spanList.length; j++) {
                const span = spanList[j]
                const prevSpan = spanList[j - 1]
                const prevSpanHeight = prevSpan.getBoundingClientRect().height
                const spanHeight = span.getBoundingClientRect().height
                const gap = (span.getBoundingClientRect().top - prevSpan.getBoundingClientRect().top - prevSpanHeight).toFixed(2)
                if (Number(gap) > (spanHeight * 0.5)) {
                    divList.push(initDiv(div, prevSpan, prevSpanHeight, data.zoom, page.getBoundingClientRect().height))
                    div = {
                        spanArr: [],
                        width: 0,
                        height: 0,
                        lineHeight: 0,
                        top: span.style.top,
                        left: span.style.left,
                    }
                }
                div.spanArr.push(span)
            }
            divList.push(initDiv(div, spanList[spanList.length - 1], div.spanArr[0].getBoundingClientRect().height, data.zoom, page.getBoundingClientRect().height))

            page.innerHTML = ''
            for (const spanArr of divList) {
                const p = document.createElement('p')
                p.style.position = 'absolute'
                p.style.top = spanArr.top
                p.style.left = spanArr.left
                // p.style.lineHeight = `calc(var(--scale-factor) * ${spanArr.lineHeight + 'px'})`
                p.style.lineHeight = 'normal'
                p.style.width = `calc(var(--scale-factor) * ${spanArr.width + 'px'})`
                p.style.height = `calc(var(--scale-factor) * ${spanArr.height + 'px'})`
                p.style.margin = '0'
                const span = document.createElement('span')
                span.style.whiteSpace = 'normal'
                span.style.fontSize = spanArr.spanArr[getMinFontSize(spanArr.spanArr)].style.fontSize
                span.style.verticalAlign = 'top'
                for (const spanItem of spanArr.spanArr) {
                    span.innerText = span.innerText + spanItem.innerText
                }
                p.appendChild(span)
                page.appendChild(p)
            }
        }
    }

    return {
        readPDF,
        resetScale
    }
}

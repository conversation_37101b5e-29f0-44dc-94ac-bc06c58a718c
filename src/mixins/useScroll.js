import {onMounted, onUnmounted, ref, watch} from "vue";
import store from "@/store";

export function useScroll(fileContentRef, translateContentRef) {
    const leftScrolling = ref(false)
    const rightScrolling = ref(false)
    const isScrollSync = ref(false)
    const timer = ref(0)

    onMounted(() => {
        fileContentRef.value.addEventListener('scroll', handleLeftScroll);
        translateContentRef.value.addEventListener('scroll', handleRightScroll);
        fileContentRef.value.addEventListener('wheel', () => {
            leftScrolling.value = true
            rightScrolling.value = false
        })
        translateContentRef.value.addEventListener('wheel', () => {
            rightScrolling.value = true
            leftScrolling.value = false
        })
    })

    const handleScroll = (sourceDiv, targetDiv, leftOrRight) => {
        const pList = sourceDiv.querySelectorAll('p');
        for (const p of pList) {
            if (getTop(p) > 0) {
                targetDiv.scrollTop = targetDiv.scrollTop + targetDiv.querySelector(`#${p.id}`).getBoundingClientRect().top - 55 - getTop(p)
                break;
            } else if (getTop(p) > 20) {
                break
            }
        }
    }
    const getTop = (p) => {
        return p.getBoundingClientRect().top - 55
    }

    const handleLeftScroll = () => {
        if (!leftScrolling.value && !store.getters.getScroll) {
            return
        }
        if (!isScrollSync.value) {
            translateContentRef.value.scrollTop = fileContentRef.value.scrollTop;
            return
        }
        const sourceDiv = fileContentRef.value;
        const targetDiv = translateContentRef.value;
        handleScroll(sourceDiv, targetDiv, 'left')
    }
    const handleRightScroll = async () => {
        if (!rightScrolling.value && !store.getters.getScroll) {
            return
        }
        if (!isScrollSync.value) {
            fileContentRef.value.scrollTop = translateContentRef.value.scrollTop;
            return
        }
        const sourceDiv = translateContentRef.value;
        const targetDiv = fileContentRef.value;
        handleScroll(sourceDiv, targetDiv, 'right')
    }
    return {
        handleLeftScroll,
        handleRightScroll
    }
}

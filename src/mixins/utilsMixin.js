import {reactive} from "vue";
import {urlToBase64} from "@/utils/commonUtils";

export function utilsMixin() {

    const data = reactive({
        idNum: 0,
        zoom: 0,
    })

    const setPId = (element, prefix) => {
        // 转换element.childNodes为数组，并对其进行遍历
        Array.from(element.childNodes).forEach((node) => {
            // 若当前节点为元素节点
            if (node.nodeType === Node.ELEMENT_NODE) {
                // 检查节点是否为<p>标签
                if (node.tagName.toLowerCase() === 'p') {
                    // 为<p>标签生成一个独一无二的id（基于前缀和当前递归的深度/索引）
                    node.id = `${prefix}${data.idNum}`; // 为<p>标签设置该id
                    data.idNum ++
                }
                // 递归调用函数，以深度遍历当前元素的所有子元素
                setPId(node, prefix);
            }
        });
    }

    //给P元素添加唯一ID
    const addUniqueIdsToPs = (element, prefix = 'p-id-') => {
        return new Promise((resolve) => {
            data.idNum = 0
            setPId(element, prefix='p-id-')
            resolve()
        })
    }

    // 替换所有 img 标签的 src
    const replaceImgSrc = (element) => {
        return new Promise(async (resolve) => {
            // 获取所有 img 标签
            const imgs = element.querySelectorAll('img');
            // 遍历每个 img 标签
            for (const img of imgs) {
                // 获取 img 标签的 src 属性值
                const src = img.getAttribute('src');
                // 替换 src 属性值
                const newSrc = await urlToBase64(src)
                console.log(newSrc)
                // 更新 img 标签的 src 属性值
                img.setAttribute('src', newSrc);
            }
            resolve()
        })
    }

    /*
    * 给元素下边的所有p 元素添加监听器，来 hover 时变色
    * @param targetElement 目标父元素
    *
    * */
    const addHoverEffect = (targetElement) => {
        targetElement.querySelectorAll('p').forEach(p => {
            p.addEventListener('mouseover', () => {
                document.querySelectorAll(`#${p.id}`).forEach(targetP => {
                    targetP.classList.add('highlight'); // 添加高亮样式
                });
            });

            p.addEventListener('mouseout', () => {
                document.querySelectorAll(`#${p.id}`).forEach(targetP => {
                    targetP.classList.remove('highlight'); // 移除高亮样式
                });
            });
        })
    }


    /*
    * 等比缩小元素内部的内容
    * */
    const setZoom = (targetElement) => {
        const docxWrapper = targetElement.querySelector('.docx-wrapper')
        // 获取宽度（px）
        const widthPx = targetElement.getBoundingClientRect().width;
        // 转换为 pt
        const widthPt = widthPx * (72 / 96);

        // 获取 docx 元素的宽度（pt）
        const docxList = docxWrapper.querySelectorAll('.docx')
        const docxPt = docxList[0].style.width.slice(0, -2)

        const zoom = 1
        data.zoom = zoom
        docxWrapper.style.transform = `scale(${zoom})`
        docxWrapper.style.transformOrigin = 'left top'
    }

    const resetZoom = (type) => {
        if (type === 'in') {
            data.zoom += 0.1;
        } else {
            if(data.zoom == 1){
                data.zoom = 1;
            }else{
                data.zoom -= 0.1;
            }
            
        }
        document.getElementById('fileContent').querySelector('.docx-wrapper').style.transform = `scale(${data.zoom})`;
        document.getElementById('translateContent').querySelector('.docx-wrapper').style.transform = `scale(${data.zoom})`;
    
        // 获取 fileContent 容器
        const fileContent = document.getElementById('fileContent');
        // 获取 translateContent 容器
        const translateContent = document.getElementById('translateContent');
    
        // 计算并设置 fileContent 水平滚动条到中间位置
        const fileScrollWidth = fileContent.scrollWidth;
        const fileClientWidth = fileContent.clientWidth;
        fileContent.scrollLeft = (fileScrollWidth - fileClientWidth) / 2;
    
        // 计算并设置 translateContent 水平滚动条到中间位置
        const translateScrollWidth = translateContent.scrollWidth;
        const translateClientWidth = translateContent.clientWidth;
        translateContent.scrollLeft = (translateScrollWidth - translateClientWidth) / 2;
    };

    return {
        addHoverEffect,
        addUniqueIdsToPs,
        replaceImgSrc,
        setZoom,
        resetZoom
    };
}

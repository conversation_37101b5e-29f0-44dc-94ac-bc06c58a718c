import {translateSearch} from "@/api/TranslateApi";
import {changeHeight} from "@/utils/commonUtils";

// 封装一个带重试机制的函数
const retryPromise = async (fn, maxRetries = 1,delay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await fn(); // 调用传入的异步函数
        } catch (error) {
            if (attempt < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, delay)); // 等待后再重试
            } else {
                console.error('已达最大重试次数，抛出错误');
                return null
            }
        }
    }
};

const translateFile = (combinedText, node, sourceLang, targetLang,translateApi) => {
    // 翻译的接口
    const translateTask = () => translateSearch({
        text: combinedText,
        source_lang: sourceLang || 'auto',
        target_lang: targetLang,
        translateApi
    });
    // 使用重试机制
    return retryPromise(translateTask).then(res => {
        if (res && res.code === 200) {
            const nodeTranslation = document.querySelector(`.translate-content #${node.id}`)
            // 清空 <p> 元素的内容
            while (nodeTranslation.firstChild) {
                nodeTranslation.removeChild(nodeTranslation.firstChild);
            }
            // 创建新的文本节点，并插入到 <p> 元素中
            const textNode = document.createElement('span');
            textNode.innerHTML = res.data.replace(/\n/g, "<span><br></span>");
            nodeTranslation.appendChild(textNode);
            return nodeTranslation
        } else {
            console.error(`翻译任务失败: ${combinedText}`);
            return null
        }
    });
};

const translateFilePDF = (combinedText, node, sourceLang, targetLang,translateApi) => {
    // 翻译的接口
    const translateTask = () => translateSearch({
        text: combinedText,
        source_lang: sourceLang || 'auto',
        target_lang: targetLang,
        translateApi
    });
    // 使用重试机制
    return retryPromise(translateTask).then(res => {
        if (res && res.code === 200) {
            node.innerText = res.data
            return node
        } else {
            console.error(`翻译任务失败: ${combinedText}`);
            return null
        }
    });
}


export const handleTranslateElement = async (element, sourceLang, targetLang,translateApi) => {
    const translationPromises = []
    for (const node of element.childNodes) {
        // 检查节点是否为元素节点且为<p>标签
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'P') {
            // p 元素下只有 span 元素
            // span 元素下有：span 元素、br、text
            const spanTexts = []
            for (const spanNode of node.childNodes) {
                if (spanNode.nodeType === Node.ELEMENT_NODE && spanNode.tagName === 'SPAN') {
                    for (const spanOrBrOrTextNodes of spanNode.childNodes) {
                        if (spanOrBrOrTextNodes.nodeType === Node.TEXT_NODE) {
                            // 如果是 text，则加入数组，等待拼接
                            spanTexts.push(spanOrBrOrTextNodes.textContent.trim())
                        } else if (spanOrBrOrTextNodes.nodeType === Node.ELEMENT_NODE && spanOrBrOrTextNodes.tagName === 'SPAN') {
                            // 如果是 span 元素，则看其子元素
                            // 子元素要么为 text，要么为 br
                            for (const textOrBrNode of spanOrBrOrTextNodes.childNodes) {
                                if (textOrBrNode.nodeType === Node.TEXT_NODE) {
                                    spanTexts.push(textOrBrNode.textContent.trim())
                                } else if (textOrBrNode.nodeType === Node.ELEMENT_NODE && textOrBrNode.tagName === 'BR') {
                                    spanTexts.push('\n')
                                }
                            }
                        } else if (spanOrBrOrTextNodes.nodeType === Node.ELEMENT_NODE && spanOrBrOrTextNodes.tagName === 'BR') {
                            spanTexts.push('\n')
                        }
                    }
                }
            }
            // 合并文本内容（可以根据需要添加分隔符）
            let combinedText = spanTexts.join(' '); // 这里使用空格作为分隔符，可以根据需要修改
            // 检查合并后的文本内容是否为空
            if (combinedText) {
                // 调用翻译函数（假设translateText是一个返回Promise的异步翻译函数）
                const translatePromise = translateFile(combinedText, node, sourceLang, targetLang,translateApi).then(translatedNode => {
                    // 翻译后立马动态更新 p 的高度
                    // 获取对应的源文件的 p
                    if (translatedNode) {
                        const sourceNode = document.querySelector(`#fileContent #${node.id}`)
                        changeHeight(sourceNode, translatedNode)
                    }
                })
                // 添加到队列中，等待最后统一请求
                translationPromises.push(translatePromise);
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            // 递归处理其他元素节点
            await handleTranslateElement(node, sourceLang, targetLang,translateApi);
        }
    }
    // 批量请求翻译
    const batchSize = 10;
    for (let i = 0; i < translationPromises.length; i += batchSize) {
        // 截取当前批次任务
        const batch = translationPromises.slice(i, i + batchSize)
        await Promise.all(batch);
    }
    return true
}

export const handleTranslatePDF = async (element, sourceLang, targetLang, translateApi) => {
    const translationPromises = []
    for (const node of element.childNodes) {
        // 检查节点是否为元素节点且为<p>标签
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'P') {
            // 调用翻译函数（假设translateText是一个返回Promise的异步翻译函数）
            const translatePromise = translateFilePDF(node.innerText, node, sourceLang, targetLang, translateApi).then(translatedNode => {
                // 翻译后立马动态更新 p 的高度
                // 获取对应的源文件的 p
                // if (translatedNode) {
                //     const sourceNode = document.querySelector(`#fileContent #${node.id}`)
                //     changeHeight(sourceNode, translatedNode)
                // }
            }).then(() => {
                if (targetLang === 'zh-CN' || targetLang === 'zh-TW' || targetLang === 'zh-HK' || targetLang === 'zh-CN' || targetLang === 'yue-CN' || targetLang === 'wuu-CN') {
                    // 中文遇到句号、问号、感叹号 换行
                    node.innerHTML = node.innerHTML.replace(/([。？！])/g, '$1<br/><br/>')
                }
            })
            // 添加到队列中，等待最后统一请求
            translationPromises.push(translatePromise);
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            // 递归处理其他元素节点
            await handleTranslatePDF(node, sourceLang, targetLang, translateApi);
        }
    }
    // 批量请求翻译
    const batchSize = 10;
    for (let i = 0; i < translationPromises.length; i += batchSize) {
        // 截取当前批次任务
        const batch = translationPromises.slice(i, i + batchSize)
        await Promise.all(batch);
    }
    return true
}

import { boot } from 'quasar/wrappers'
import ElementUI from 'element-plus'
import 'element-plus/theme-chalk/index.css'
import '@/style/element-theme.scss'
import '@/style/element-theme-dark.scss'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

export default boot(({ app }) => {
  // element-plus 图标引入
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }

  app.use(ElementUI)
})
export { ElementUI }

<template>
    <div class="headerBox">
        <div class="headerLogo">
            <img src="@/assets/gt.jpg" alt="" style="width: 25px;height: 25px;margin-right: 10px">
            TerpMate
        </div>
        <div class="headerButton">
            <div class="buttonLeft" :style="{ 'width': isShowRight ? 'calc(100% - 70px)' : 'calc(100% - 200px)' }">
                <div class="translate-mode-select">
                    <el-select v-model="translateApi" filterable>
                        <el-option
                            v-for="item in translateModelList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </div>
                
                <el-input
                    ref="inputRef"
                    v-model="searchValue"
                    @keyup.enter="handleTranslate"
                    placeholder="Enter Text To Translate..."
                >
                </el-input>
                
                <el-card v-show="translateSearchVisible" class="translateBox" ref="translateBox" shadow="always" body-style="padding:10px 20px 30px">
                    <el-row justify="center">
                        <el-button class="langCheckButton" type="info" text @click="changeRightLeft">
                            <span>{{leftLang.show}}</span>
                            <el-icon><ArrowRightBold /></el-icon>
                            <span>{{rightLang.show}}</span>
                        </el-button>
                    </el-row>
                    <el-row>
                        <el-button circle text :type="isGlossary ? 'warning' : ''" bg @click="handleGlossary">
                            <svg class="svg-inline--fa fa-star fa-w-18 fa-lg text-muted" aria-hidden="true" focusable="false" data-prefix="far" data-icon="star" role="img" xmlns="http://www.w3.org/2000/svg" width="22" height="17" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M528.1 171.5L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6zM388.6 312.3l23.7 138.4L288 385.4l-124.3 65.3 23.7-138.4-100.6-98 139-20.2 62.2-126 62.2 126 139 20.2-100.6 98z"></path></svg>
                        </el-button>
                        <span class="searchClass">&nbsp{{searchValue}}&nbsp</span>
                        <el-button circle text :type="isReplace ? 'warning' : ''" bg @click="replaceVisible = true">
                            <svg t="1642064481841" class="fa-lg fa-fw" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5017" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="17" fill="currentColor"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 200 200" style="enable-background:new 0 0 200 200;" xml:space="preserve"><path d="M189.3,155.8c-1.7-2.7-4-5.1-6.6-7c2.7-4,4.3-9,4.3-14.2c0-12.7-8.8-22.9-19.7-22.9h-33.2c-6,0-10.9,4.9-10.9,10.9v62.6 c0,6,4.9,10.9,10.9,10.9h33c7.1,0,13.7-2.8,18.7-7.8c5-5,7.7-11.5,7.6-18.5C193.4,164.8,192,159.9,189.3,155.8L189.3,155.8z M175.4,178c-2.2,2.2-5.1,3.4-8.1,3.4h-29.5v-23.1h29.5c3,0,5.9,1.2,8.1,3.4c2.2,2.2,3.4,5.1,3.4,8.1S177.6,175.8,175.4,178 L175.4,178z M170.2,141.1c-0.4,0.5-1.6,1.7-2.9,1.7h-29.5v-16.6h29.5c1.4,0,2.5,1.2,2.9,1.7c1.4,1.6,2.2,4.1,2.2,6.6 C172.4,137,171.6,139.5,170.2,141.1L170.2,141.1z M89.6,82.8L63.3,16.9c-1.1-2.8-3.9-13-13.4-13l0,0c-9.7,0-12.4,10.2-13.5,13 L10.2,82.4c-1.5,3.8,0.1,8.3,3.8,9.9c3.8,1.6,8.2-0.2,9.8-4.1l8.7-21.7h34.8l8.7,21.7c1.2,2.9,3.9,4.7,6.9,4.7 c0.9,0,1.9-0.2,2.8-0.5C89.3,90.9,91.2,86.6,89.6,82.8z M61.3,51.8H38.4l11.5-30.9 M109.5,171.6l-23.4,13.8c-1.2,0.7-2.5,1-3.8,1 c-2.5,0-5-1.3-6.4-3.6c-2.1-3.5-0.9-8.1,2.6-10.1l8-4.7c-9.7-3-18.2-7.2-25.5-12.8c-8.3-6.2-14.9-14-19.8-23.2 c-8.2-15.5-8.4-29.6-8.4-30.2v0c0-3.2,2-6.1,5-7c5.1-1.6,9.8,2.2,9.8,7v0c0,0.1,0.2,11.5,6.9,23.9c3.8,7.1,9.1,13.1,15.6,17.9 c6,4.4,13,7.9,21.1,10.3l-4.7-9.1c-1.9-3.6-0.5-8.1,3.1-10c3.6-1.9,8.1-0.5,10,3.1l12.5,23.9C114.2,165.3,112.9,169.6,109.5,171.6z"></path></svg></svg>
                        </el-button>
                    </el-row>
                    <el-row style="margin-top: 20px">
                        <el-input ref="editTranslateInput" v-model="translateRes" v-if="editTranslate" @keyup.enter="editGlossary"></el-input>
                        <span v-else class="translateResBox" v-loading="translateLoading" @click="showEditTranslate">{{ translateLoading ? '' : translateRes}}</span>
                    </el-row>
                    <el-row style="margin-top: 20px;color: #FF4500;display: flex;align-items: center;font-size: 12px;margin-left: 20px" v-if="operate === 'delete' && !editTranslate">
                        <el-icon><DeleteFilled /></el-icon>
                        <span> Delete Success!</span>
                    </el-row>
                    <el-row style="margin-top: 20px;color: #2DCE89;display: flex;align-items: center;font-size: 12px;margin-left: 20px" v-if="operate === 'add' && !editTranslate">
                        <el-icon><SuccessFilled /></el-icon>
                        <span> Add Success!</span>
                    </el-row>
                    <el-row style="margin-top: 20px;color: #525f7f;display: flex;align-items: center;font-size: 12px;font-weight: bold" v-if="editTranslate">
                        <span>Press enter to add to Vocabulary</span>
                    </el-row>
                </el-card>
            </div>

            <div class="buttonRight">
                <div class="emailAddress" v-if="!isShowRight">{{ emailAddress }}</div>
                
                <el-popover placement="bottom" :width="200">
                    <template #reference>
                        <img src="@/assets/opacity.png" alt="" style="width: 23px;height: 23px;margin-right: 5px">
                    </template>
                    <div class="slider-demo-block">
                        <span class="demonstration">Opacity</span>
                        <el-slider size="small" :show-tooltip="false" :min="0.5" :max="1" :step="0.01" v-model="windowOpacityValue" />
                    </div>
                </el-popover>

                <el-tooltip class="box-item" effect="dark" content="Consecutive Interpretation Mode" placement="top-start">
                    <img v-if="!openConsecutive" @click="consecutiveInterpretationClickLocal" src="@/assets/jiaochuan-no.png" alt="" style="width: 23px;height: 23px;margin-right: 5px">
                    <img v-if="openConsecutive" @click="consecutiveInterpretationClickLocal" src="@/assets/jiaochuan-yes.png" alt="" style="width: 23px;height: 23px;margin-right: 5px">
                </el-tooltip>

                <el-popover placement="bottom" :width="100" trigger="hover" v-if="openConsecutive && speakerArr.length > 0">
                    <template #reference>
                        <img src="@/assets/hide-speaker.png" alt="" style="width: 23px;height: 23px;margin-right: 5px; cursor: pointer;">
                    </template>
                    <div class="speaker-checkbox-container">
                        <div class="speaker-title">Hide Speaker:</div>
                        <div
                            v-for="speaker in speakerArr"
                            :key="speaker.name"
                            class="speaker-checkbox-item"
                        >
                            <el-checkbox
                                v-model="speaker.hide"
                                @change="onSpeakerCheckboxChange(speaker)"
                            >
                                {{ speaker.name.charAt(0) }}
                            </el-checkbox>
                        </div>
                    </div>
                </el-popover>

                <el-tooltip class="box-item" effect="dark" content="Pin" placement="top-start">
                    <img v-if="!openPin" @click="pinClick" src="@/assets/pin-no.png" alt="" style="width: 23px;height: 23px;margin-right: 5px">
                    <img v-if="openPin" @click="pinClick" src="@/assets/pin-yes.png" alt="" style="width: 23px;height: 23px;margin-right: 5px">
                </el-tooltip>
                <el-tooltip class="box-item" effect="dark" content="Always Top" placement="top-start">
                    <img v-if="!stayTop" @click="staytopClick" src="@/assets/staytop-no.png" alt="" style="width: 23px;height: 23px;margin-right: 5px">
                    <img v-if="stayTop" @click="staytopClick" src="@/assets/staytop-yes.png" alt="" style="width: 23px;height: 23px;margin-right: 5px">
                </el-tooltip>
                 <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="If no audio devices are populated, please click."
                    placement="bottom"
                >
                <el-button type="primary" size="small" v-if="platform === 'mac'" plain style="margin-left: 10px" @click="resetSystemAudio">Reset Audio</el-button>
                 </el-tooltip>
                <el-popconfirm width="220px" title="Are you sure to logout?" @confirm="logout">
                    <template #reference>
                        <el-button type="primary" size="small" plain style="margin-left: 10px">Logout</el-button>
                    </template>
                </el-popconfirm>
            </div>
        </div>
        <el-dialog
            v-model="replaceVisible"
            :append-to-body="true"
            ref="replaceDialog"
            title="Force Replace"
            width="40%"
            align-center
            center
        >
            <div class="replaceDiv">
                <span>{{searchValue}}</span>
                <span>
                    <svg class="svg-inline--fa fa-arrow-right fa-w-14" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="arrow-right" role="img" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"></path></svg>
                </span>
                <el-input v-model="replaceValue"></el-input>
            </div>
            <template #footer>
                <div>
                    <el-button type="primary" @click="handleSaveReplace">
                        Confirm
                    </el-button>
                    <el-button @click="replaceVisible = false">Cancel</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { onBeforeUnmount, onMounted, ref, watch, computed } from "vue";
import router from "@/router";
import {ArrowRightBold, DeleteFilled, SuccessFilled} from "@element-plus/icons-vue";
import {translateSearch} from "@/api/TranslateApi";
import {ElMessage} from "element-plus";
import {addGlossary, addGlossaryScene, deleteGlossary} from "@/api/GlossaryApi";
import moment from "moment";
import { Platform } from 'quasar'
import store from "@/store";
import {addReplace, deleteReplace} from "@/api/ReplaceApi";
import { resetMacSystemAudio } from "@/utils/macControl"
import {getTranslateModelList} from "@/api/TranslateApi";
const {ipcRenderer} = require('electron')
const windowOpacityValue = ref(1)
const props = defineProps({
    theme: {type: Boolean},
    langList: {type: Array},
    glossaryRes: {type: Array},
    glossarySceneId: {type: Number},
    replaceRes: {type: Array},
    replaceSceneId: {type: Number},
    checkIsGlossaryScene: {type: Function},
    checkIsReplaceScene: {type: Function},
    changeGlossaryScene: {type: Function},
    isShowRight: {type: Boolean},
    isShowRightButton: {type: Boolean},
    handleShowRight: {type: Function},
    openConsecutive: {type: Boolean},
    consecutiveInterpretationClick: {type: Function},
    speakerArr: {type: Array},
    speakerStatusChange: {type: Function}
})
const platform = computed(() => {
  return Platform.is.win ? 'window' : Platform.is.mac ? 'mac' : null
});
// 是否展示翻译框
const translateSearchVisible = ref(false)
// 搜索值
const searchValue = ref('')
// 翻译表中存在当前搜索值
const isGlossary = ref(false)
// 替换表中存在当前搜索值
const isReplace = ref(false)
// 邮箱地址
const emailAddress = ref('')
const inputRef = ref(null)
const translateBox = ref(null)
const replaceDialog = ref(null)
// 翻译成功前进行loading
const translateLoading = ref(true)
// 搜索值得翻译结果
const translateRes = ref('')
// 替换的值
const replaceValue = ref('')
// 左侧语言
const leftLang = ref({code: 'en-US', show: 'English-United States', isShow: false, main: true},)
// 右侧语言
const rightLang = ref({code: 'zh-CN', show: 'Chinese-Mandarin', isShow: false, second: true})
// 操作类型 delete || add
const operate = ref('')
const replaceVisible = ref(false)
// 是否展示输入框，修改翻译的值
const editTranslate = ref(false)
// 编辑翻译值的输入框的ref
const editTranslateInput = ref(null)
const stayTop = ref(false)
const openPin = ref(false)

const translateModelList = ref([])
const translateApi = ref(null)
onMounted(async () => {
    let res = await getTranslateModelList()
    translateModelList.value = res.data
    translateApi.value = res.data[0].id
    emailAddress.value = localStorage.getItem('emailAddress')
    window.addEventListener('mousedown', handleOutsideClick)
})
onBeforeUnmount(() => {
    window.removeEventListener('mousedown', handleOutsideClick);
})

watch(
    () => windowOpacityValue.value,
    (newValue, oldValue) => {
        ipcRenderer.send("changeWindowOpacity",windowOpacityValue.value);
    }
)

watch(
    () =>　props.langList,
    (newValue, oldValue) => {
        // 过滤掉Auto选项，查词功能只使用Primary和Secondary语言
        const filteredLangList = newValue.filter(item => item.code !== 'Auto');

        // 如果存在Auto，使用Primary和Secondary语言进行翻译
        if (newValue.some(item => item.code === 'Auto')) {
            const primaryLang = newValue.find(item => item.main);
            const secondaryLang = newValue.find(item => item.second);

            if (primaryLang && secondaryLang) {
                leftLang.value = primaryLang;
                rightLang.value = secondaryLang;
            }
        } else {
            // 没有Auto时，使用原有逻辑
            filteredLangList.forEach(item => {
                if (item.isShow){
                    leftLang.value = item
                } else {
                    rightLang.value = item
                }
            })
        }
    }
)
watch(
    () => operate.value,
    (newValue, oldValue) => {
        setTimeout(() => {
            operate.value = ''
        }, 1000)
    }
)
watch(
    () => store.getters.getSearchValue,
    (newValue, oldValue) => {
        if (newValue){
            searchValue.value = newValue
            handleTranslate()
        }
    }
)
const resetSystemAudio = ()=>{
   resetMacSystemAudio()
}
// 置顶
const staytopClick = ()=>{
    stayTop.value = !stayTop.value
    ipcRenderer.send("staytopClick",stayTop.value);
}
const consecutiveInterpretationClickLocal = ()=>{
    props.consecutiveInterpretationClick()
}

// 复选框变化处理
const onSpeakerCheckboxChange = (speaker) => {
    props.speakerStatusChange(speaker)
    console.log('Speaker checkbox changed:', speaker.name, 'hide:', speaker.hide)
}
// 禁止拖动
const pinClick = ()=>{
    openPin.value = !openPin.value
    ipcRenderer.send("pinClick",openPin.value);
}

const cancelTranslate = () => {
    isGlossary.value = false
    isReplace.value = false
    translateLoading.value = true
    operate.value = ''
    replaceValue.value = ''
    translateSearchVisible.value = false
    searchValue.value = ''
    editTranslate.value = false
}
const logout = () => {
    localStorage.removeItem('userData')
    router.push({
        path: 'login',
        query: {
            first: false
        }
    })
    ElMessage.success('Logout Success！')
}
const changeRightLeft = () => {
    translateLoading.value = true
    const item = leftLang.value
    leftLang.value = rightLang.value
    rightLang.value = item
    translateSearch({
        text: searchValue.value,
        source_lang: leftLang.value.code,
        target_lang: rightLang.value.code,
        translateApi: translateModelList.value.find( item=> item.id === translateApi.value)
    }).then(res =>　{
        translateLoading.value = false
        translateRes.value = res.data
    })
}
const handleGlossary = async () => {
    if (isGlossary.value) {
        // 翻译数组中存在当前搜索的值，再次点击为删除
        const glossaryItem = props.glossaryRes.find(item => item.glossaryA === searchValue.value)
        deleteGlossary({ids: [glossaryItem.id]}).then(res => {
            if (res.success) {
                props.checkIsGlossaryScene(glossaryItem.sceneId)
                operate.value = 'delete'
                isGlossary.value = false
            }
        })
    } else {
        if (!props.glossarySceneId) {
            // 当翻译场景id未空时，先新建一个场景，然后再添加
            const res = await addGlossaryScene({name: moment().format("YYYY-MM-DD")})
            if (res.success) {
                props.changeGlossaryScene(res.data.id)
                addGlossary({
                    glossaryA: searchValue.value,
                    glossaryB: translateRes.value,
                    sceneId: res.data.id
                }).then(res => {
                    if (res.success) {
                        props.checkIsGlossaryScene(props.glossarySceneId)
                        operate.value = 'add'
                        isGlossary.value = true
                    }
                })
                return
            }
        }
        // 翻译数组中不存在当前搜索的值，再次点击为新增
        addGlossary({
            glossaryA: searchValue.value,
            glossaryB: translateRes.value,
            sceneId: props.glossarySceneId[0]
        }).then(res => {
            if (res.success) {
                props.checkIsGlossaryScene(props.glossarySceneId[0])
                operate.value = 'add'
                isGlossary.value = true
            }
        })
    }
}
const handleTranslate = () => {
    // 设置打开页面的样式 开始
    translateBox.value.$el.style.top = '36px'
    translateBox.value.$el.style.width = inputRef.value.$el.offsetWidth + 'px'
    translateBox.value.$el.focus()
    // 设置打开页面的样式 结束
    translateSearchVisible.value = true

    // 判断当前搜索的值是否在替换、翻译表中存在 开始
    const findGlossaryIndex = props.glossaryRes.findIndex(item => item.glossaryA === searchValue.value)
    if (findGlossaryIndex !== -1) {
        // 如果翻译集中存在搜索的值，则直接去翻译值，不走谷歌翻译接口
        isGlossary.value = true
        translateRes.value = props.glossaryRes[findGlossaryIndex].glossaryB
        translateLoading.value = false
        return
    }
    const findReplaceIndex = props.replaceRes.findIndex(item => item.replaceA === searchValue.value)
    if (findReplaceIndex !== -1) {
        isReplace.value = true
        replaceValue.value = props.replaceRes[findReplaceIndex].replaceB
    }
    // 判断当前搜索的值是否在替换、翻译表中存在 结束

    translateSearch({
        text: searchValue.value,
        source_lang: leftLang.value.code,
        target_lang: rightLang.value.code,
        translateApi: translateModelList.value.find( item=> item.id === translateApi.value)
    }).then(res =>　{
        translateLoading.value = false
        translateRes.value = res.data
    })
}
const editGlossary = async () => {
    if (!props.glossarySceneId) {
        // 当翻译场景id未空时，先新建一个场景，然后再添加
        const res = await addGlossaryScene({name: moment().format("YYYY-MM-DD")})
        if (res.success) {
            props.changeGlossaryScene(res.data.id)
            const glossaryItem = props.glossaryRes.find(item => item.glossaryA === searchValue.value)
            addGlossary({
                ...glossaryItem,
                glossaryA: searchValue.value,
                glossaryB: translateRes.value,
                sceneId: res.data.id
            }).then(res => {
                if (res.success) {
                    props.checkIsGlossaryScene(props.glossarySceneId)
                    operate.value = 'add'
                    isGlossary.value = true
                    editTranslate.value = false
                }
            })
            return
        }
    }
    const glossaryItem = props.glossaryRes.find(item => item.glossaryA === searchValue.value)
    addGlossary({
        ...glossaryItem,
        glossaryA: searchValue.value,
        glossaryB: translateRes.value,
        sceneId: props.glossarySceneId
    }).then(res => {
        if (res.success) {
            props.checkIsGlossaryScene(props.glossarySceneId)
            operate.value = 'add'
            isGlossary.value = true
            editTranslate.value = false
        }
    })
}
// 点击展示输入框，来修改翻译的值
const showEditTranslate = () => {
    editTranslate.value = true
}
const handleOutsideClick = (event) => {
    // event.preventDefault()
    if (translateBox.value.$el.contains(event.target) || replaceDialog.value?.dialogContentRef?.$el.contains(event.target)) {
        if (editTranslate.value && editTranslateInput.value.$el.contains(event.target)) return
        editTranslate.value = false;
        return
    }
    cancelTranslate()
};
const handleSaveReplace = () => {
    if (isReplace.value) {
        // 在替换集中，存在当前搜索值。
        // 如果替换值为空，则删除。否则更新。
        const findReplace = props.replaceRes.find(item => item.replaceA === searchValue.value)
        if (replaceValue.value) {
            addReplace({...findReplace, replaceB: replaceValue.value, sceneId:props.replaceSceneId}).then(res => {
                if (res.success) {
                    props.checkIsReplaceScene(props.replaceSceneId)
                    isReplace.value = true
                    replaceVisible.value = false
                }
            })
        } else {
            deleteReplace({ids:[findReplace.id]}).then(res => {
                if (res.success) {
                    props.checkIsReplaceScene(props.replaceSceneId)
                    isReplace.value = false
                    replaceVisible.value = false
                }
            })
        }
    } else {
        // 如果替换值为空，则不进行操作。否则新增。
        if (replaceValue.value) {
            addReplace({replaceA: searchValue.value, replaceB: replaceValue.value, sceneId:props.replaceSceneId}).then(res => {
                if (res.success) {
                    props.checkIsReplaceScene(props.replaceSceneId)
                    isReplace.value = true
                    replaceVisible.value = false
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.slider-demo-block {
  max-width: 600px;
  display: flex;
  align-items: center;
  padding: 5px;
}
.slider-demo-block .demonstration{
    margin-right: 15px;
}

.headerBox {
    width: 100%;
    height: 40px;
    padding: 0 20px;
    display: flex;
    justify-content: flex-start;

    .headerLogo {
        width: 160px;
        padding: 2px;
        display: flex;
        align-items: center;
        letter-spacing: 2px;
        font-size: 22px;
        color: #07c160;
        border-radius: 4px;
    }

    .headerButton {
        width: calc(100% - 160px);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .buttonLeft {
            width: calc(100% - 210px);
            display: flex;
            justify-content: flex-start;
        }

        .buttonRight {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #8898aa;

            .emailAddress {
                margin-right: 10px;
            }
        }

        .translate-mode-select{
            width: 150px;
            margin-right: 5px;
        }
    }
}

.translateBox {
    z-index: 2;
    box-sizing: border-box;
    position: fixed;

    .searchClass {
        font-family: Open Sans, sans-serif;
        line-height: 32px
    }

    .langCheckButton {
        font-weight: bolder;
        font-family: inherit
    }

    .translateResBox {
        margin-left: 40px;
        font-size: 18px;
        font-weight: bolder;
    }
}

.replaceDiv {
    display: flex;
    align-items: center;

    span:first-child {
        line-height: 32px;
        margin-bottom: 4px;
        white-space: nowrap;
    }

    span {
        margin-right: 10px;
    }
}

.noSelect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

@media (max-width: 500px) {
    .emailAddress {
        display: none;
    }

    .buttonLeft {
        display: none !important;
    }

    .buttonRight {
        display: none !important;
    }
}
.searchInput {
    height: 32px;
    .relative-position {
        height: 32px;
    }
    :deep(.relative-position) {
        height: 32px;
}
}

/* 说话人复选框样式 */
.speaker-checkbox-container {
    padding: 2px 0;
}

.speaker-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 2px;
    color: #606266;
}

.speaker-checkbox-item {
    margin-bottom: 0px;
}

.speaker-checkbox-item:last-child {
    margin-bottom: 0;
}
</style>

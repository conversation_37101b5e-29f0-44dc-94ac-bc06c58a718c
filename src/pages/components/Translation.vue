<template>
    <div :class="{translationBox: true, darkTheme: !theme}">
        <el-scrollbar
            ref="scrollbar"
            height="calc(100vh - 80px)"
            id="parentScroll"
            @mouseup="handleMouseUp"
            @mousedown="handleMouseDown"
            @touchend="highlightText"
            @scroll="handleBarScroll"
        >
            <div
                v-for="item in renderedRes"
                :key="item.id"
                class="translation-item"
                :style='getFontSize()'
                ref="translationItemRef"
                @scroll="handleItemScroll($event, item.id)"
                @wheel="handleItemWheel($event, item.id)"
            >
                <el-divider :key="item.id" class="timeLine" content-position="center">{{ item.id }}</el-divider>
                <p
                    v-for="res in item.result"
                    v-html="res.transcript"
                    :key="res.id"
                    @contextmenu.prevent="showContextMenu($event, res)"
                    :class="{ 'hidden-text': hiddenTexts.has(res.id) }"
                ></p>
            </div>
        </el-scrollbar>
        
        <!-- 添加浮动箭头按钮 -->
        <div v-show="showScrollButton" class="scroll-to-bottom" @click="scrollBtnClick">
            <el-icon :size="20"><ArrowDown /></el-icon>
        </div>

        <!-- 右击菜单 -->
        <div
            v-show="contextMenuVisible"
            class="context-menu"
            :style="{ top: contextMenuTop + 'px', left: contextMenuLeft + 'px' }"
            @click="hideContextMenu"
        >
            <div class="menu-item" @click="hideText">
                <el-icon><Hide /></el-icon>
                <span>Hide</span>
            </div>
        </div>

        <!-- 点击遮罩层隐藏菜单 -->
        <div
            v-show="contextMenuVisible"
            class="context-menu-overlay"
            @click="hideContextMenu"
        ></div>
    </div>
</template>

<script setup>
import { ArrowDown, Hide } from '@element-plus/icons-vue'
import {nextTick, onMounted, onBeforeUnmount, ref, render, watch, shallowRef, computed} from "vue";
import store from "@/store";
const { ipcRenderer } = require('electron')

const text = ref(null)
const scrollbar = ref()
const searchValue = ref('')
const visible = ref(false)
const top = ref(0)
const left = ref(0)
const currentFirst = ref(-1)
const translationItemRef = ref()
const showScrollButton = ref(false)
const props = defineProps({
    translationRes: {type: Array},
    fontSize: {type: Number},
    theme: {type: Boolean},
    isStart: {type: Boolean},
    autoScroll: {type: Boolean},
    speakerArr: {type: Array},
    hideSpeaker: {type: Function},
    openConsecutive: {type: Boolean}
})
const mouseDownTimer = ref(null)
const renderedRes = shallowRef([])
const autoScroll = computed(() => props.autoScroll)

// 右击菜单相关变量
const contextMenuVisible = ref(false)
const contextMenuTop = ref(0)
const contextMenuLeft = ref(0)
const currentContextItem = ref(null)
const hiddenTexts = ref(new Set())

const renderDom = ref(true)
watch(
    searchValue,
    (newValue, oldValue) => {
        store.commit('setSearchValue', newValue)
    }
)
watch(
    () => props.translationRes,
    (newValue) => {
        if (renderDom.value) {
            updateRenderedRes(newValue)
        }
    },
    { deep: true }
)


const updateRenderedRes = (newValue) => {
    if (!renderDom.value) return
    // 克隆一份，避免响应式联动
    renderedRes.value = JSON.parse(JSON.stringify(newValue))
    updateTextVisibilityBySpeaker(props.speakerArr)
    nextTick(() => {
        if(autoScroll.value){
            scrollToBottom()
        }
    })
}


watch(
    () => store.getters.getColor,
    (color) => {
        console.log('color', color)
        const colorSpan = document.querySelectorAll('#colorSpan')
        colorSpan.forEach(item => {
            item.style.color = color;
        })
    }
)

// 根据speakerArr对文本进行显示或隐藏处理
const updateTextVisibilityBySpeaker = (speakerArr) => {
    renderedRes.value.forEach(item => {
        item.result.forEach(res => {
            const prefix = speakerArr.find(speaker => res.original.startsWith(speaker.name))
            // 根据说话人的hide状态决定是否隐藏文本
            if (prefix && prefix.hide) {
                hiddenTexts.value.add(res.id)
            } else {
                hiddenTexts.value.delete(res.id)
            }
        })
    })
}

watch(
    () => props.speakerArr,
    (newSpeakerArr, oldSpeakerArr) => {
        console.log('speakerArr changed:', newSpeakerArr)
        console.log('old speakerArr:', oldSpeakerArr)
        // 调用文本显示隐藏处理方法
        updateTextVisibilityBySpeaker(newSpeakerArr)
    },
    { deep: true }
)

const getFontSize = () => {
    return {'font-size': props.fontSize + 'px'}
}
const isWrap = (item) => {
    if (item.id === -1) {
        return {width: '100%'}
    }
}
const handleTouchStart = e => {
    console.log('TouchStart执行一次')
    e.preventDefault()//添加阻止click事件触发
}
const getSearch = (fatherNode, search = '') => {
    const curNodeType = fatherNode.nodeType;
    // 将节点类型为元素节点，并且设置了user-select:none的节点从复制的内容中过滤掉
    if (curNodeType === 1 && fatherNode.style.userSelect === 'none') return search

    const nodeList = fatherNode.childNodes
    if (nodeList.length === 0) {
        search += fatherNode.data || ''
    } else {
        for (let i = 0; i < nodeList.length; i++) {
            const curChildNode = nodeList[i]
            search = getSearch(curChildNode, search)
        }
    }
    return search
}
const highlightText = (event) => {
    // event.preventDefault()
    const selection = window.getSelection()
    if (!selection.rangeCount) return
    const copyNode = selection.getRangeAt(0).cloneContents();
    cancelSelection()
    searchValue.value = getSearch(copyNode)
}

const cancelSelection = () => {
    // 创建一个临时输入框并将其放在页面外面，用于接收焦点并取消选择
    let input = document.createElement("input");
    input.setAttribute("type", "text");
    input.style.position = "absolute";
    input.style.left = "-9999px";

    // 将输入框添加到DOM中
    document.body.appendChild(input);

    // 接收焦点并取消选择
    input.focus();
    input.setSelectionRange(0, 0);

    // 移除临时输入框
    document.body.removeChild(input);
}
const clearSearchValue = () => {
    searchValue.value = ''
    document.getSelection().empty()
}
const scrollBtnClick = ()=>{
    scrollbar.value.scrollTo({
        top: 99999999,
        behavior: 'smooth'
    })
    translationItemRef.value[translationItemRef.value.length - 1].scrollTo({
        top: 99999999,
        behavior: 'smooth'
    })
    showScrollButton.value = false
}
// 滚动至最底部
const scrollToBottom = () => {
    if(showScrollButton.value) return
    scrollbar.value.scrollTo({
        top: 99999999,
        behavior: 'smooth'
    })
    // 当在识别中时，将最后一个元素实时滚动到底部
    // if (props.isStart) {
        translationItemRef.value[translationItemRef.value.length - 1].scrollTo({
            top: 99999999,
            behavior: 'smooth'
        })
    // }
}
// 翻译滚动至本次开始处
const scrollToPrevious = () => {
    const overElementList = document.getElementsByClassName('first')
    if (overElementList.length === 0) {
        return
    }
    if (currentFirst.value === -1) {
        currentFirst.value = overElementList.length - 1
    }
    // 暂时停止继续往上
    // else if (currentFirst.value > 0){
    //     currentFirst.value = currentFirst.value - 1
    // }
    const element = overElementList[currentFirst.value]
    scrollbar.value.setScrollTop(element.offsetTop)
    nextTick(() => {
        element.classList.add('blinking')
        setTimeout(() => {
            element.classList.remove('blinking');
        }, 1000);
    })
}
const topContent  = () => {
    const element = document.getElementById("parentScroll");
    const visibleHeight = element.offsetHeight;
    const minh = visibleHeight - 2
    document.getElementById('scrollBox').style.paddingBottom = minh + "px"
    scrollToBottom()
}
const onMouseMove = ()=>{
    console.log('onMouseMove ')
}
const handleItemWheel = (event, itemId) => {
    if (event.deltaY < 0) {
        showScrollButton.value = true;
    } 
}
const handleBarScroll = (event) => {
    const scrollWrap = scrollbar.value.wrapRef;
    if (!scrollWrap) return;
    
    // 检查是否滚动到底部
    const height = Math.abs(scrollWrap.scrollHeight - scrollWrap.scrollTop - scrollWrap.clientHeight);
    if (height < 1) {
        showScrollButton.value = false;
    }
}
const handleItemScroll = (event, itemId) => {
    const target = event.target;
    // 检查是否滚动到底部
    const height = Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight);
    if (height < 1) {
        showScrollButton.value = false;
    }
}
const handleMouseDown = () => {
    mouseDownTimer.value = setTimeout(() => {
        // 长按超过500ms后的处理
        console.log('Long press detected')
        renderDom.value = false
    }, 50)
}

const handleMouseUp = () => {
    console.log('press up detected')
    renderDom.value = true
    if (mouseDownTimer.value) {
        clearTimeout(mouseDownTimer.value)
        mouseDownTimer.value = null
    }
    highlightText()
}

// 右击菜单相关方法
const showContextMenu = (event, item) => {
    event.preventDefault()
    // 只有在交传模式下才显示右击菜单
    if (!props.openConsecutive) {
        return
    }
    currentContextItem.value = item
    contextMenuTop.value = event.clientY
    contextMenuLeft.value = event.clientX
    contextMenuVisible.value = true
}

const hideContextMenu = () => {
    contextMenuVisible.value = false
    currentContextItem.value = null
}

const hideText = () => {
    console.log(props.speakerArr)
    console.log(currentContextItem.value.original)
    let prefix = null
    props.speakerArr.forEach(item=>{
        if(currentContextItem.value.original.startsWith(item.name)){
            prefix = item.name
        }
    })
    if(prefix){
        //prefix是隐藏的元素的前缀，此时最后一个内容中，有多个文本以这个前缀开头，都要隐藏
        renderedRes.value[renderedRes.value.length - 1].result.forEach(item=>{
            if(item.original.startsWith(prefix)){
                hiddenTexts.value.add(item.id)
            }
        })
        props.hideSpeaker(prefix)
    }
    hideContextMenu()
}

// 键盘滚动功能
const scrollStep = 100 // 每次滚动的像素距离

const handleKeyDown = (event) => {
    // 只有在没有开启自动滚动时才启用键盘滚动
    if (props.autoScroll) {
        return
    }

    switch (event.code) {
        case 'Space':
            event.preventDefault()
            scrollDown()
            break
        case 'ArrowUp':
            event.preventDefault()
            scrollUp()
            break
        case 'ArrowDown':
            event.preventDefault()
            scrollDown()
            break
    }
}

const scrollDown = () => {
    console.log('scrollDown called, translationItemRef.value:', translationItemRef.value)
    if (translationItemRef.value && translationItemRef.value.length > 0) {
        // 获取最后一个translation-item元素
        const lastItem = translationItemRef.value[translationItemRef.value.length - 1]
        if (lastItem) {
            const currentScrollTop = lastItem.scrollTop
            const newScrollTop = currentScrollTop + scrollStep
            console.log('Scrolling translation-item down from', currentScrollTop, 'to', newScrollTop)
            lastItem.scrollTo({
                top: newScrollTop,
                behavior: 'smooth'
            })
        }
    } else {
        console.log('translationItemRef not available')
    }

    // 发送滚动事件到realtimeTranslate窗口
    ipcRenderer.send('sync-scroll', { direction: 'down', step: scrollStep })
}

const scrollUp = () => {
    console.log('scrollUp called, translationItemRef.value:', translationItemRef.value)
    if (translationItemRef.value && translationItemRef.value.length > 0) {
        // 获取最后一个translation-item元素
        const lastItem = translationItemRef.value[translationItemRef.value.length - 1]
        if (lastItem) {
            const currentScrollTop = lastItem.scrollTop
            const newScrollTop = Math.max(0, currentScrollTop - scrollStep)
            console.log('Scrolling translation-item up from', currentScrollTop, 'to', newScrollTop)
            lastItem.scrollTo({
                top: newScrollTop,
                behavior: 'smooth'
            })
        }
    } else {
        console.log('translationItemRef not available')
    }

    // 发送滚动事件到realtimeTranslate窗口
    ipcRenderer.send('sync-scroll', { direction: 'up', step: scrollStep })
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
    window.addEventListener('keydown', handleKeyDown)
})

// 组件卸载时移除键盘事件监听
onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown)
})

defineExpose({scrollToPrevious, scrollToBottom, topContent})//暴露子组件的方法
</script>

<style scoped lang="scss">
$top-footer-height: 80px;
$item-margin-top: 10px;

.darkTheme {
    p {
        color: #fff !important;
    }
}

p {
    margin: 0;
    padding: 0;
    transition: opacity 0.3s ease;
}

.hidden-text {
    // opacity: 0.3;
    // text-decoration: line-through;
    // color: #ccc !important;
    display: none;
}

.translationBox {
    height: calc(100vh - $top-footer-height);
    width: 98vw;
    border-radius: 4px;
    margin: 0 17px;

    .translation-item {
        height: calc(100vh - $top-footer-height - $item-margin-top);
        margin-top: $item-margin-top;
        overflow: scroll;
        padding-top: 10px;
        position: relative;
    }
    .translation-item::-webkit-scrollbar {
        display: none;
    }
}

.scrollBox {
    width: 98%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.scrollBox p {
    font-weight: 400;
    line-height: 1.5;
    font-family: Open Sans, sans-serif;
    color: #525f7f;
    display: inline;
    white-space: pre-wrap;
}

.contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}
.timeLine {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #07c160;
    position: sticky;
    top: 0;


    ::v-deep .el-divider__text.is-center {
        color: #07c160;
    }
}
.blinking {
    animation: blink 0.8s 1; /* 2s 表示动画执行时间，1 表示动画只执行一次 */
}
.el-divider--horizontal {
    margin: 10px 0;
}
/* 定义一个闪烁动画 */
@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0; }
    100% { opacity: 1; }
}
@supports(height: 100dvh) {
    .container { height: 100dvh; }
    .loginBox { height: 100dvh; }
    .rightBoxTop { height: 50dvh; }
    .rightBoxBottom { height: 50dvh; }
}

.scroll-to-bottom {
    position: fixed;
    right: 40px;
    bottom: 100px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #07c160;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    z-index: 99;

    &:hover {
        background-color: #06a452;
        transform: translateY(-2px);
    }
}

/* 右击菜单样式 */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 120px;
    padding: 4px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
    transition: background-color 0.3s;
}

.menu-item:hover {
    background-color: #f5f7fa;
}

.menu-item .el-icon {
    margin-right: 8px;
    font-size: 16px;
}

.context-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background: transparent;
}
</style>

<script setup>
import {readExcelToJson} from "@/utils/xlsxUtil";
import {reactive, watch} from "vue";
import {ElMessage} from "element-plus";
import {addGlossaryScene, batchAddGlossary, getAllGlossaryScene} from "@/api/GlossaryApi";
import {addReplaceScene, batchAddReplace, getAllReplaceScene} from "@/api/ReplaceApi";

const props = defineProps({
    // 词汇表还是替换表
    currentScene: {
        type: String,
        default: "glossary"
    },
    // 文件拖拽中反馈窗口
    uploading: {
        type: Boolean,
        default: false
    },
    // 场景 id
    sceneId: {
        type: Number,
        default: -1
    },
    // 场景名称列表
    allGlossarySceneData: {
        type: Array,
        default: () => []
    },
    langList: {
        type: Array,
        default: () => []
    },
    afterUpload: {
        type: Function,
        default: () => {
        }
    },
    afterAddScene: {
        type: Function,
        default: () => {
        }
    }
})
const emit = defineEmits(["update:uploading"])

const data = reactive({
    // 拖拽结束展示上传数据窗口
    showFileData: false,
    // 拖拽数据列表
    dropFileData: [],
    Ashow: "",
    Bshow: "",
    loading: false,
    buttonLoading: false,
    sceneIdLocal: null,
})

watch(
    () => props.langList,
    (newValue, oldValue) => {
        newValue.forEach(item => {
            if (item.main) {
                data.Ashow = `A(${item.show})`
            } else {
                data.Bshow = `B(${item.show})`
            }
        })
    }
)

// 拖拽上传功能
// dragover必须阻止默认行为
const handleFileDrop = event => {
    event.preventDefault();
    data.loading = true
    emit("update:uploading", false)
    data.showFileData = true
    const file = event.dataTransfer.files[0];
    readExcelToJson(file).then(res => {
        if (props.currentScene === 'glossary') {
            res.map(item => {
                if (item.length !== 2) return
                // 转换存储格式
                const newItem = {
                    glossaryA: item[0],
                    glossaryB: item[1]
                }
                data.dropFileData.push(newItem)
            })
        } else if (props.currentScene === 'replace') {
            res.map(item => {
                if (item.length !== 2) return
                // 转换存储格式
                const newItem = {
                    replaceA: item[0],
                    replaceB: item[1]
                }
                data.dropFileData.push(newItem)
            })
        }
        data.loading = false
    }).catch((err) => {
        data.loading = false
        data.showFileData = false
        ElMessage.error('Import fail！')
    })
}
// 拖拽文件移动的操作
const handleDragOver = event => {
    event.preventDefault();
}
// 拖拽文件离开的操作
const handleDragLeave = event => {
    event.preventDefault();
    if (!event.fromElement) {
        emit("update:uploading", false)
    }
}
// 拖拽批量新增窗口取消的方法
const cancelDropAdd = () => {
    data.dropFileData = []
    data.showFileData = false
    data.sceneIdLocal = null
}
// 拖拽批量新增窗口关闭的回调
const handleDropClose = (done) => {
    cancelDropAdd()
    props.afterAddScene()
    done()
}
// 拖拽文件数据保存
const saveDropData = async () => {
    let id;
    let addRes;
    let getRes;
    if (props.sceneId === -1 && typeof data.sceneIdLocal === 'string'){
        if (props.currentScene === 'glossary') {
            getRes = await getAllGlossaryScene({})
        } else if (props.currentScene === 'replace') {
            getRes = await getAllReplaceScene({})
        }
        id = getRes.data.find(item => item.name === data.sceneIdLocal).id
    } else {
        id = data.sceneIdLocal
    }
    const dropData = {
        sceneId: props.sceneId === -1 ? id : props.sceneId,
        batchAddData: data.dropFileData
    }
    if (props.currentScene === 'glossary') {
        addRes = await batchAddGlossary(dropData)
    } else if (props.currentScene === 'replace') {
        addRes = await batchAddReplace(dropData)
    }
    if (addRes.success) {
        ElMessage.success('Add Success!')
        cancelDropAdd()
        props.afterUpload()
    } else {
        ElMessage.error(addRes.msg)
    }
}
const handleSelectScene = async (scene) => {
    if (typeof scene === 'string') {
        data.buttonLoading = true
        let addRes;
        if (props.currentScene === 'glossary') {
            addRes = await addGlossaryScene({name: scene})
        } else if (props.currentScene === 'replace') {
            addRes = await addReplaceScene({name: scene})
        }
        if (addRes.success) {
            ElMessage.success(`Name of ${scene} Add Success!`)
            data.buttonLoading = false
        } else {
            ElMessage.error(addRes.msg)
            data.buttonLoading = false
        }
    }
}
</script>

<template>
    <div>
        <!-- 拖拽上传的展示的窗口 -->
        <el-dialog
            :model-value="uploading"
            fullscreen
            @drop="handleFileDrop"
            @dragleave="handleDragLeave"
            @dragover="handleDragOver"
            :show-close="false"
        >
            <div class="uploading">
                <img src="@/assets/upload.png" alt="upload">
                <span>Drop *.xls, *.xlsx file here to import!</span>
            </div>
        </el-dialog>

        <!-- 拖拽文件数据确认窗口 -->
        <el-dialog
            v-model="data.showFileData"
            :before-close="handleDropClose"
            draggable
        >
            <template v-if="sceneId === -1">
                <el-row style="margin-bottom: 10px">
                    <span style="font-weight: bolder;color: #b25252">{{`Select or Enter the ${currentScene==='glossary' ? 'Vocabulary' : 'Replace'} name you want to import   `}}</span>
                </el-row>
                <el-row style="margin-bottom: 10px" align="middle">
                    Choose Name For Import:
                    <el-select v-model="data.sceneIdLocal" allow-create filterable default-first-option @change="handleSelectScene" placeholder="Select or Input New Name" style="width: 260px">
                        <el-option
                            v-for="item in props.allGlossarySceneData"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-row>
            </template>
            <el-row>
                <el-table v-loading="data.loading" :data="data.dropFileData" border max-height="55dvh">
                    <el-table-column type="index" label="Index" width="100" align="center"/>
                    <template v-if="currentScene === 'glossary'">
                        <el-table-column prop="glossaryA" :label="data.Ashow" align="center">
                            <template #default="{row}">
                                <el-input v-model="row.glossaryA"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="glossaryB" :label="data.Bshow" align="center">
                            <template #default="{row}">
                                <el-input v-model="row.glossaryB"></el-input>
                            </template>
                        </el-table-column>
                    </template>
                    <template v-else-if="currentScene === 'replace'">
                        <el-table-column prop="replaceA" :label="data.Ashow" align="center">
                            <template #default="{row}">
                                <el-input v-model="row.replaceA"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="replaceB" :label="data.Bshow" align="center">
                            <template #default="{row}">
                                <el-input v-model="row.replaceB"></el-input>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
            </el-row>
            <el-row justify="end" style="margin-top: 10px">
                <el-button type="primary" @click="saveDropData" :loading="data.buttonLoading">Save</el-button>
            </el-row>
        </el-dialog>
    </div>
</template>

<style scoped lang="scss">
.uploading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 40dvh;
    width: 30dvw;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    background-color: #07c160;
    position:fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);

    span {
        font-size: 18px;
        font-weight: bolder;
        margin-top: 20px;
        color: #f6f6f6;
    }
}
</style>

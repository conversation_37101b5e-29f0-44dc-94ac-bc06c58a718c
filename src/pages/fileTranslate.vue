<template>
    <div class="container">
        <!-- 左侧文件树 -->
        <div class="tree-box" :style="{'width': data.fileTreeWidth+'px'}">
            <el-button @click="selectFolder" type="primary" style="width: 100%">Choose Folder</el-button>
            <el-tree
                class="file-tree-box"
                :data="data.fileTree"
                node-key="id"
                :props="{
                    label: 'name',
                    children: 'children'
                }"
                :default-expanded-keys="data.expandList"
                @node-click="handleOpenFile"
                @node-expand="handleNodeExpand"
                @node-collapse="handleNodeCollapse"
            >
                <template #default="{ node, data }">
                    <div>
                        <el-icon v-if="data.type === 'directory'" style="margin-right: 5px;"><Folder /></el-icon>
                        <el-tooltip
                            effect="dark"
                            :content="node.label"
                            placement="top-start"
                            :show-arrow="false"
                            :enterable="false"
                        >
                            <span>{{ node.label }}</span>
                        </el-tooltip>
                    </div>
                </template>
            </el-tree>
        </div>
        <!-- 中间的分割线 -->
        <div class="divider-center" @mousedown="handleResize"></div>
        <!-- 右侧文件内容和翻译 -->
        <div class="show-box" :style="{'width': 'calc(100vw - ' + data.fileTreeWidth + 'px - 5px)'}">
            <!-- 顶部操作栏 -->
            <div class="show-top-box">
                <div class="target-select q-ml-sm">
                    <!--<div class="target-select-title">Source Language:</div>-->
                    <el-select v-model="data.sourceLang" filterable placeholder="Source Language">
                        <el-option
                            v-for="item in langListParams"
                            :key="item.code"
                            :label="item.show"
                            :value="item.code"
                        />
                    </el-select>
                </div>
                <div class="flex items-center" @click="switchSourceAndTargetLanguage">
                    <svg t="1732500744075" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1604" width="18" height="18"><path d="M871.24 443.3H120.56a32 32 0 0 1 0-64h750.68a32 32 0 0 1 0 64z" p-id="1605" fill="#07c160"></path><path d="M875.42 443.3a31.86 31.86 0 0 1-21.24-8L627.72 234a32 32 0 1 1 42.52-48l226.46 201.38a32 32 0 0 1-21.28 56zM875.44 646.7H124.76a32 32 0 1 1 0-64h750.68a32 32 0 0 1 0 64z" p-id="1606" fill="#07c160"></path><path d="M347.02 848a31.88 31.88 0 0 1-21.26-8L99.3 638.62a32 32 0 1 1 42.52-47.84L368.28 792a32 32 0 0 1-21.26 56z" p-id="1607" fill="#07c160"></path></svg>
                </div>
                <div class="target-select">
                    <!--<div class="target-select-title">Target Language:</div>-->
                    <el-select v-model="data.targetLang" filterable placeholder="Target Language">
                        <el-option
                            v-for="item in langListParams"
                            :key="item.code"
                            :label="item.show"
                            :value="item.code"
                        />
                    </el-select>
                </div>
                <div class="target-select translate-select">
                    <!--<div class="target-select-title">Target Language:</div>-->
                    <el-select v-model="data.translateApi" filterable>
                        <el-option
                            v-for="item in translateModelList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </div>
                <el-button
                    :disabled="data.translateLoading"
                    circle
                    @click="handleTranslateFile"
                    class="themeWhite"
                >
                    <!--翻译图标的svg-->
                    <svg width="16" height="16" fill="#07c160" t="1732087341968" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16539"><path d="M885.76 426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667A204.373333 204.373333 0 0 0 640 181.76a42.666667 42.666667 0 0 1 0-85.333333A289.706667 289.706667 0 0 1 928.426667 384a42.666667 42.666667 0 0 1-42.666667 42.666667zM407.893333 896a289.706667 289.706667 0 0 1-289.28-289.28 42.666667 42.666667 0 0 1 85.333334 0A203.946667 203.946667 0 0 0 407.893333 810.666667a42.666667 42.666667 0 1 1 0 85.333333zM424.106667 516.266667H177.92A92.586667 92.586667 0 0 1 85.333333 423.68V177.493333A92.586667 92.586667 0 0 1 177.92 85.333333h246.186667a92.586667 92.586667 0 0 1 92.586666 92.586667v245.76a92.586667 92.586667 0 0 1-92.586666 92.586667zM177.92 146.773333a31.146667 31.146667 0 0 0-31.146667 30.72v246.186667a31.146667 31.146667 0 0 0 31.146667 31.146667h246.186667a31.146667 31.146667 0 0 0 30.72-31.146667V177.493333a30.72 30.72 0 0 0-30.72-30.72z" p-id="16540"></path><path d="M354.986667 363.093333H247.04a70.4 70.4 0 0 1-70.826667-70.4v-14.08a70.826667 70.826667 0 0 1 70.826667-70.826666h107.946667A70.4 70.4 0 0 1 426.666667 278.613333v14.08a70.4 70.4 0 0 1-71.68 70.4z m-107.946667-93.866666a9.386667 9.386667 0 0 0-9.386667 9.386666v14.08a9.386667 9.386667 0 0 0 9.386667 8.96h107.946667a8.96 8.96 0 0 0 8.96-8.96v-14.08a8.96 8.96 0 0 0-8.96-9.386666z" p-id="16541"></path><path d="M300.8 441.6a31.146667 31.146667 0 0 1-30.72-31.146667V190.72a30.72 30.72 0 0 1 30.72-30.72 31.146667 31.146667 0 0 1 31.146667 30.72v219.733333a31.146667 31.146667 0 0 1-31.146667 31.146667zM846.08 938.666667h-246.186667a92.586667 92.586667 0 0 1-92.586666-92.586667v-246.186667a92.586667 92.586667 0 0 1 92.586666-92.586666h246.186667A92.586667 92.586667 0 0 1 938.666667 599.893333v246.186667A92.586667 92.586667 0 0 1 846.08 938.666667z m-246.186667-369.493334a30.72 30.72 0 0 0-30.72 30.72v246.186667a31.146667 31.146667 0 0 0 30.72 31.146667h246.186667a31.146667 31.146667 0 0 0 31.146667-31.146667v-246.186667a31.146667 31.146667 0 0 0-31.146667-30.72z" p-id="16542"></path><path d="M832.853333 849.066667a30.72 30.72 0 0 1-26.453333-15.36l-110.08-190.293334a30.72 30.72 0 0 1 53.333333-31.146666l110.08 190.72a30.72 30.72 0 0 1-11.093333 42.666666 30.293333 30.293333 0 0 1-15.786667 3.413334z" p-id="16543"></path><path d="M613.12 849.066667a29.013333 29.013333 0 0 1-15.36-4.266667 30.293333 30.293333 0 0 1-11.52-42.666667l110.08-190.72a30.72 30.72 0 0 1 53.333333 31.146667L640 833.706667a30.293333 30.293333 0 0 1-26.88 15.36z" p-id="16544"></path><path d="M796.586667 783.36h-145.493334a31.146667 31.146667 0 0 1 0-61.866667h145.493334a31.146667 31.146667 0 0 1 0 61.866667z" p-id="16545"></path></svg>
                </el-button>
                <el-button
                    v-if="data.fileType === 'docx'"
                    :disabled="data.translateLoading"
                    circle
                    @click="handleSaveFile"
                    class="themeWhite"
                >
                    <!--保存图标的svg-->
                    <svg t="1732196762050" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1582" width="16" height="16"><path d="M925.248 356.928l-258.176-258.176a64 64 0 0 0-45.248-18.752H144a64 64 0 0 0-64 64v736a64 64 0 0 0 64 64h736a64 64 0 0 0 64-64V402.176a64 64 0 0 0-18.752-45.248zM288 144h192V256H288V144z m448 736H288V736h448v144z m144 0H800V704a32 32 0 0 0-32-32H256a32 32 0 0 0-32 32v176H144v-736H224V288a32 32 0 0 0 32 32h256a32 32 0 0 0 32-32V144h77.824l258.176 258.176V880z" p-id="1583" fill="#07c160"></path></svg>
                </el-button>
                <el-button
                    :disabled="data.translateLoading"
                    @click="openContrastFile"
                    circle
                    class="themeWhite"
                >
                    <!-- <svg width="16" height="16" fill="#07c160" t="1732089109428" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21158"><path d="M153.6 902.656a32.256 32.256 0 0 1 0-64h716.8a32.256 32.256 0 0 1 0 64z m390.656-665.6v494.592a32.256 32.256 0 0 1-64 0V237.056L236.032 481.28a31.744 31.744 0 1 1-45.056-45.056l294.912-295.424a36.864 36.864 0 0 1 51.2 0l294.912 294.912a31.744 31.744 0 0 1-45.056 45.056z" p-id="21159"></path></svg> -->
                    <svg t="1732196837156" class="icon" viewBox="0 0 1077 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3767" width="16" height="16"><path d="M463.5648 0a89.6 89.6 0 0 1 58.9952 22.1568l2.816 2.5856L817.024 302.6688a89.6 89.6 0 0 1 27.712 61.0048l0.0768 3.8528V537.6a38.4 38.4 0 0 1-76.736 2.2528L768 537.6V396.8H531.2a89.6 89.6 0 0 1-89.5488-86.528L441.6 307.2V76.8H89.6a12.8 12.8 0 0 0-12.7104 11.3024L76.8 89.6v793.6a12.8 12.8 0 0 0 11.3024 12.7104L89.6 896h183.808a38.4 38.4 0 0 1 2.2656 76.736L273.408 972.8H89.6a89.6 89.6 0 0 1-89.5488-86.528L0 883.2V89.6a89.6 89.6 0 0 1 86.528-89.5488L89.6 0h373.9648z m260.3136 320L518.4 124.1728V307.2a12.8 12.8 0 0 0 11.3024 12.7104L531.2 320h192.6784z" fill="#07c160" p-id="3768"></path><path d="M620.8 524.8a38.4 38.4 0 0 1 38.336 36.1472L659.2 563.2v153.6H806.4a38.4 38.4 0 0 1 2.2528 76.736L806.4 793.6H659.2v140.8a38.4 38.4 0 0 1-76.736 2.2528L582.4 934.4V793.6H435.2a38.4 38.4 0 0 1-2.2528-76.736L435.2 716.8h147.2V563.2a38.4 38.4 0 0 1 38.4-38.4z" fill="#07c160" p-id="3769"></path></svg>
                </el-button>
                <!--全局搜索框-->
                <div class="target-select search-box">
                    <el-input
                        v-model="data.searchValue"
                        placeholder="Search"
                        @input="handleSearchContent"
                        @keyup.enter="handleNextSearch"
                    >
                        <template #suffix>
                            <el-icon @click="handleSearchContent" style="cursor: pointer"><search /></el-icon>
                        </template>
                    </el-input>
                </div>
                <!--向上查找-->
                <el-icon
                    color="#07c160"
                    class="find-button"
                    @click="handlePrevSearch"
                ><ArrowUpBold /></el-icon>
                <!--向下查找-->
                <el-icon
                    color="#07c160"
                    class="find-button"
                    @click="handleNextSearch"
                ><ArrowDownBold /></el-icon>
                <div class="right-area">
                    <el-select v-model="rightFontSize" placeholder="Select" style="width: 100px" @change="rightFontSizeChange">
                        <el-option
                            v-for="item in rightFontSizeArr"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <!--放大缩小-->
                    <div class="zoom-button">
                        <el-icon class="zoom-in" size="20" @click="handleZoom('in')"><ZoomIn /></el-icon>
                        <el-icon class="zoom-out" size="20" @click="handleZoom('out')"><ZoomOut /></el-icon>
                    </div>
                </div>
            </div>
            <!-- 下方展示内容 -->
            <div class="show-Bottom-box">
                <!-- 文件内容 -->
                <div
                    ref="fileContentRef"
                    v-loading="data.readLoading"
                    id="fileContent"
                    class="file-content"
                >
                </div>
                <!-- 翻译结果 -->
                <div
                    ref="translateContentRef"
                    id="translateContent"
                    class="translate-content"
                    contenteditable="true"
                    @keydown.enter="handleEnter"
                >
                </div>
            </div>
        </div>
       
    </div>
</template>

<script setup>
import {h, nextTick, onBeforeUnmount, onMounted, reactive, ref} from "vue";
import {useRouter} from "vue-router";
import {langListParams, translateApiParams} from "@/utils/commonParams";
import * as docx from "docx-preview";
import Message from "@/utils/Message";
import {utilsMixin} from '@/mixins/utilsMixin'
import {ElMessageBox} from "element-plus";
import {ArrowDownBold, ArrowUpBold, Folder, Search, ZoomIn, ZoomOut} from "@element-plus/icons-vue";
import {handleTranslateElement, handleTranslatePDF} from "@/mixins/useTranslate";
import {handleChangeHeight} from "@/utils/commonUtils";
import {usePDF} from "@/mixins/usePDF";
import {useScroll} from "@/mixins/useScroll";
import {pdf2word} from "@/api/FileConversionApi";
import {getTranslateModelList} from "@/api/TranslateApi";
import store from "@/store";
const path = require('path');
const fs = require('fs');




const { ipcRenderer } = require('electron')

const router = useRouter();

const fileContentRef = ref(null)
const translateContentRef = ref(null)
const timeout = ref(0)

const data = reactive({
    fileTreeWidth: document.documentElement.clientWidth * 0.2,
    fileTree: [], //文件树
    expandList: ['root'], // 文件树打开的节点
    fileType: '', //文件类型 docx、pdf
    translateRes: '',
    top: '', //右键菜单出现的位置
    left: '', //右键菜单出现的位置
    showContextmenu: false, //右键菜单是否显示
    selectFile: {},  //右键菜单选中的文件
    sourceLang: '', //源语言
    targetLang: '', //目标语言
    readLoading: false, //读取文件内容时显示加载动画
    translateLoading: false, //翻译时显示加载动画
    isResizing: false, // 左侧文件树是否正在改变宽度
    startX: 0, // 拖拽文件树宽度时的鼠标X坐标,
    translateApi: null,
    searchValue: '',
    searchIndex: 0,
})

const { addUniqueIdsToPs, addHoverEffect, setZoom, resetZoom } = utilsMixin(); //给 p 标签添加 id
const { readPDF, resetScale } = usePDF()
const {} = useScroll(fileContentRef, translateContentRef)
const translateModelList = ref(null)
// 定义 rightFontSizeArr 为一个数组
const rightFontSizeArr = ref([
  { value: 'Normal', label: 'Normal' },
  { value: 'Medium', label: 'Medium' },
  { value: 'Large', label: 'Large' }
]);
const rightFontSize = ref('Normal');

// 挂载和卸载时添加/移除事件监听
onMounted(async () => {
    let res = await getTranslateModelList()
    translateModelList.value = res.data
    data.translateApi = res.data[0].id
    document.addEventListener("click", handleClickOutside);
    // 获取打开的文件夹的改变
    watchFolderChange()
    data.fileTree = await ipcRenderer.invoke('get-root-folder');
    let bilingualReaderLangInfo = await ipcRenderer.invoke("getBilingualReaderLangInfo", null)
    console.log('bilingualReaderLangInfo ',bilingualReaderLangInfo)
    if(bilingualReaderLangInfo){
        data.sourceLang = bilingualReaderLangInfo.source
        data.targetLang = bilingualReaderLangInfo.target
    }
});

onBeforeUnmount(() => {
    document.removeEventListener("click", handleClickOutside);
});

const rightFontSizeChange = ()=>{
    let fontSize = 10.5;
    rightFontSize.value == 'Normal' ?  fontSize = 10.5 : 
    rightFontSize.value == 'Medium' ? fontSize = 13 : fontSize = 15.5
    console.log(rightFontSize.value)
    changeFontSize(fontSize)
}
const changeFontSize = (fontSize)=>{
    const targetP = translateContentRef.value.querySelectorAll('p')
    for (let i = 0; i < targetP.length; i++) {
        targetP[i].style.fontSize = fontSize + 'pt';
        targetP[i].style.height = '';
    }
    handleChangeHeight(fileContentRef.value, translateContentRef.value)
}
const switchSourceAndTargetLanguage = ()=>{
    const mid = data.sourceLang
    data.sourceLang = data.targetLang
    data.targetLang = mid
}
const close = () => {
    router.push('/interpretease')
}
const watchFolderChange = () => {
    ipcRenderer.on('folder-changed', (event, res) => {
        console.log('文件树改变', data.fileTree)
        data.fileTree = res.files
    })
}
const selectFolder = async () => {
    data.fileTree = await ipcRenderer.invoke('select-folder');
    console.log('文件树', data.fileTree)
}
// 文件夹打开时，进行保存。方便后面文件树更新后，维持原来的展开状态
const handleNodeExpand = (expand) => {
    data.expandList.push(expand.id)
}
// 文件夹关闭时，进行保存。方便后面文件树更新后，维持原来的展开状态
const handleNodeCollapse = (collapse) => {
    data.expandList.splice(data.expandList.indexOf(collapse.id), 1)
}
const handleOpenFile = (item) => {
    if (item.type === 'directory') {
        return
    }
    // 每次打开文件清空右侧翻译内容
    document.getElementById('translateContent').innerHTML = ''

    data.selectFile = item
    const fileType = data.selectFile.name.split('.').pop();
    data.fileType = fileType
    if (fileType === 'docx') {
        handleOpenDocx(fileType)
        return;
    }
    if (fileType === 'pdf') {
        handleOpenPDF()
    }
}

// 打开 docx 的处理
const handleOpenDocx = (fileType) => {
    data.readLoading = true
    // 解析docx文件，解析的内容放到左侧内容区域
    docxToHtml(data.selectFile.path, document.getElementById("fileContent")).then(async () => {
        // 将源文件的 img src 替换为 base64    --转成 base64也保存不到 docx 中，先不转了
        // await replaceImgSrc(document.getElementById('fileContent'))

        // 判断文件是否为翻译后的文件，如果是则右侧打开源文件
        const regex = /_([a-zA-Z]{2})-/;
        if (regex.test(data.selectFile.path)) {
            const sourceFilePath = data.selectFile.path.replace(/_(.*?)\.(?=[^]*$)/, '.');
            const res = await docxToHtml(sourceFilePath, document.getElementById("translateContent"))
            if (res) {
                Message.success('Successfully displaying the source file')
                // 对齐处理
                handleChangeHeight(fileContentRef.value, translateContentRef.value)
                return
            }
        }

        // 判断该文件是否已经存在对应语言的翻译文件
        // 如果有，则直接读取，否则复制文件内容
        if (data.targetLang) {
            const targetFilePath = data.selectFile.path.replace(/\.[^/.]+$/, '') + '_' + data.targetLang + '.' + fileType
            const res = await docxToHtml(targetFilePath, document.getElementById("translateContent"))
            if (res) {
                Message.success('Successfully displaying the translated file')
                // 对齐处理
                handleChangeHeight(fileContentRef.value, translateContentRef.value)
                return
            }
        }
        // 遍历 sourceElement 的子节点，将每个子节点克隆后插入 targetElement
        Array.from(document.getElementById('fileContent').childNodes).forEach((child,index) => {
            const clonedChild = child.cloneNode(true); // 克隆节点
            document.getElementById('translateContent').appendChild(clonedChild);
            // 给所有的 p 元素添加 hover 监听
            addHoverEffect(document.getElementById('translateContent'))
        });
    });
}
const handleOpenPDF = () => {
    fileContentRef.value.innerHTML = ''
    // 读取 pdf
    const filePath = data.selectFile.path
    const dirPath = path.dirname(filePath);
    const fileNameWithoutExt = path.basename(filePath, path.extname(filePath));
    const docxFilePath = path.join(dirPath, `${fileNameWithoutExt}.docx`);
    fs.access(docxFilePath, fs.constants.F_OK, async (err) => {
        if (err) {
            data.readLoading = true
            //先进行转docx，再打开docx
            const res = await pdf2word(filePath,docxFilePath)
            console.log('pdf2word res ',res)
            data.selectFile.path = docxFilePath
            data.fileType = 'docx'
            data.readLoading = false
            handleOpenDocx('docx')
        } else {
            //直接打开docx
            data.selectFile.path = docxFilePath
            data.fileType = 'docx'
            handleOpenDocx('docx')
        }
    });
    // readPDF(data.selectFile.path)

}
// 点击页面其他区域时隐藏菜单
const handleClickOutside = (event) => {
    if (!event.target.closest('#contextmenu')) {
        data.showContextmenu = false
    }
};
/*
* 将 docx 文件转换为 html
*
* @param path 文件地址
* @param targetElement 将内容赋值给的 目标元素
*
* @return Promise对象 没有返回值
* */
const docxToHtml = async (path, targetElement) => {
    const fileBuffer = await ipcRenderer.invoke('read-file', path, null, {
        inWrapper: false
    });
    if (fileBuffer) {
        return docx.renderAsync(
            fileBuffer,
            targetElement,
            null,
            {
                renderHeaders: false,
                renderFooters: false
            }
        ).then(() => {
            // 去除打开的文件开头的空白 p
            const pList = targetElement.querySelectorAll('p')
            for (const p of pList) {
                if (p.innerText.trim() === '') {
                    p.remove()
                } else {
                    // 跳出循环
                    break
                }
            }
            const imgList = targetElement.querySelectorAll('img');
            for (const img of imgList) {
                img.style.zIndex = '-10';
            }
        }).then(async () => {
            // 给源文件的p 添加 id
            await addUniqueIdsToPs(targetElement);
            // 给所有的 p 元素添加 hover 监听
            await addHoverEffect(targetElement)
            // 根据targetElement的宽度，等比缩放
            await setZoom(targetElement)
            return true
        })
    } else {
        return false
    }
}
const handleDeleteFile = () => {
    data.showContextmenu = false
    ElMessageBox({
        title: 'Delete',
        message: h('p', null, [
            h('span', null, 'Are you sure to delete  '),
            h('i', { style: 'color: #07c160' }, data.selectFile.name),
        ]),
        showCancelButton: true,
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
                instance.confirmButtonLoading = true
                ipcRenderer.invoke('delete-file', data.selectFile.path).then(() => {
                    instance.confirmButtonLoading = false
                    done()
                    Message.success('Successfully deleted')
                })
            } else {
                done()
            }
        },
    })
}
//开始翻译
const handleTranslateFile = async () => {
    if (document.getElementById("fileContent").innerHTML === '') {
        Message.warning('Please Choose File')
        return
    }
    if (!data.sourceLang) {
        Message.warning('Please Select Source Language')
        return
    }
    if (!data.targetLang) {
        Message.warning('Please Select Target Language')
        return
    }
    //保存选中的source lang 和 target lang
    ipcRenderer.send('saveBilingualReaderLangInfo',{source: data.sourceLang,target: data.targetLang})
    data.translateRes = ''
    data.translateLoading = true
    const translateModel = translateModelList.value.find(item => item.id === data.translateApi);
    let resizeFontSizeInterval = setInterval(() => {
        rightFontSizeChange()
    }, 1000);
    if (data.fileType === 'docx') {
        await handleTranslateElement(document.querySelector('.file-content .docx-wrapper'), data.sourceLang, data.targetLang,translateModel)
    }
    if (data.fileType === 'pdf') {
        await handleTranslatePDF(document.querySelector('.translate-content .pdf-wrapper'), data.sourceLang, data.targetLang,translateModel)

    }
    clearInterval(resizeFontSizeInterval)
    Message.success('Translation over!')
    rightFontSizeChange()
    data.translateLoading = false
}
// 存储文件
const handleSaveFile = async () => {
    if (document.getElementById("translateContent").innerHTML === '') {
        Message.warning('Please Choose File')
        return
    }
    // 判断是否选择了语言 未选择不让存储
    if (!data.targetLang) {
        Message.warning('Please Select Target Language')
        return
    }
    // 拼接翻译的样式内容
    let style = ''
    const target = document.getElementById('translateContent')
    target.childNodes.forEach(node => {
        if (node.tagName === 'STYLE') {
            console.log(typeof node.textContent)
            style += node.textContent
        }
    })
    // 获取翻译的文档内容
    const docxWrapper = document.querySelector('#translateContent .docx-wrapper')
    const htmlString = `
        <!doctype html>
        <html>
            <head>
                <style> ${style} </style>
            </head>
            <body> ${docxWrapper.outerHTML} </body>
        </html>
    `;
    // 设置要保存文件地址
    const pathArr = data.selectFile.path.split('.')
    // 拼接文件地址
    // /xxx/xxx/aaa.docx  =>  /xxx/xxx/aaa_en-US.docx
    const downPath = pathArr[0] + '_' + data.targetLang + '.' + pathArr[1]
    const saveRes = await ipcRenderer.invoke('write-file', downPath, htmlString);
    if (!saveRes) {
        Message.success('Save Success!')
    } else {
        Message.error('Save Failed!')
    }
}
const handleResize = (event) => {
    data.isResizing = true
    data.startX = event.pageX
    document.addEventListener('mousemove', resize);
    document.addEventListener('mouseup', stopResizing);
    document.body.style.userSelect = 'none';
}
const resize = (event) => {
    if (data.isResizing) {
        const newWidth = data.fileTreeWidth + (event.pageX - data.startX);
        data.startX = event.pageX
        const maxWidth = document.querySelector('.container').offsetWidth - 100; // Subtract resizer width and some padding
        const minWidth = 50; // Minimum width for the left div

        if (newWidth >= minWidth && newWidth <= maxWidth) {
            data.fileTreeWidth = newWidth;
        }
    }
}
const stopResizing = () => {
    data.isResizing = false;
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopResizing);
    document.body.style.userSelect = '';
};
const openContrastFile = async () => {
    if (document.getElementById("fileContent").innerHTML === '') {
        Message.warning('Please select the source file first！')
        return
    }
    data.filePath = await ipcRenderer.invoke('select-contrast-file');
    docxToHtml(data.filePath, document.getElementById("translateContent")).then(() => {
        // 对齐处理
        handleChangeHeight(fileContentRef.value, translateContentRef.value)
    })
}
const handleZoom = (type) => {
    // pdf 文件处理
    if (data.fileType === 'pdf') {
        resetScale(type)
        return
    }
    if (data.fileType === 'docx') {
        resetZoom(type)
    }
}
const handleEnter = (e) => {
    if (data.fileType === 'docx') {
        e.preventDefault();
    }
}
// 搜索方法
const handleSearchContent = async () => {
    // 清空上次搜索
    data.searchIndex = 0
    const spanList = document.querySelectorAll('.search-result')
    for (let i = 0; i < spanList.length; i++) {
        const span = spanList[i]
        const parent = span.parentNode;
        parent.replaceChild(document.createTextNode(span.textContent), span);
    }
    // 等待页面渲染完成
    await nextTick()

    // 防抖
    clearTimeout(timeout.value)
    timeout.value = setTimeout(() => {
        if (!data.searchValue) return
        const regex = new RegExp(data.searchValue, 'i'); // 动态创建正则，忽略大小写
        const find = []
        const spanListFile = fileContentRef.value.querySelectorAll('p span')
        for (let i = 0; i < spanListFile.length; i++) {
            const span = spanListFile[i]
            if (regex.test(span.textContent)) {
                find.push(span)
            }
        }
        const spanListTranslate = translateContentRef.value.querySelectorAll('p span')
        for (let i = 0; i < spanListTranslate.length; i++) {
            const span = spanListTranslate[i]
            if (regex.test(span.textContent)) {
                find.push(span)
            }
        }
        if (find.length > 0) {
            // 创建忽略大小写的全局正则表达式
            const regex = new RegExp(data.searchValue, 'ig');
            for (let i = 0; i < find.length; i++) {
                const span = find[i]
                if (i === 0) {
                    span.innerHTML = span.innerHTML.replace(regex, match => {
                        return `<font class="search-result current-result">${match}</font>`
                    })
                } else {
                    span.innerHTML = span.innerHTML.replace(regex, match => {
                        return `<font class="search-result">${match}</font>`
                    })
                }
            }
        }
    }, 300)
}
// 查找下一个搜索目标
const handleNextSearch = async () => {
    store.commit('setScroll', true)
    const searchRes = document.querySelectorAll('.search-result')
    // 清除上一个目标的特殊高亮
    const prevSpan = searchRes[data.searchIndex]
    prevSpan.classList.remove('current-result')
    // 添加下一个目标的高亮
    if (data.searchIndex === (searchRes.length - 1)) {
        data.searchIndex = 0
    } else {
        data.searchIndex++
    }
    const nextSpan = searchRes[data.searchIndex]
    nextSpan.classList.add('current-result')
    await nextSpan.scrollIntoView()
    setTimeout(() => {
        store.commit('setScroll', false)
    }, 200)
}
// 查找上一个搜索目标
const handlePrevSearch = async () => {
    store.commit('setScroll', true)
    const searchRes = document.querySelectorAll('.search-result')
    const prevSpan = searchRes[data.searchIndex]
    prevSpan.classList.remove('current-result')
    if (data.searchIndex === 0) {
        data.searchIndex = searchRes.length - 1
    } else {
        data.searchIndex--
    }
    const nextSpan = searchRes[data.searchIndex]
    nextSpan.classList.add('current-result')
    await nextSpan.scrollIntoView()
    setTimeout(() => {
        store.commit('setScroll', false)
    }, 200)
}
</script>

<style scoped lang="scss">
@import '@/css/common.scss';
@import "/public/pdf/pdf_viewer.css";

.container {
    display: flex;
    width: 100vw;
    height: 100vh;
}
.close-box {
    position: fixed;
    right: 20px;
    top: 18px;
    cursor: pointer;
}
.tree-box {
    height: 100%;
    padding: 10px;

    display: flex;
    flex-direction: column;
    align-items: center;
}
.file-tree-box {
    width: 100%;
    height: calc(100% - 40px);
    overflow-x: hidden;
}
.file-tree-box::-webkit-scrollbar {
    display: none;
}
.contextmenu {
    width: 80px;
    background: #07c160;
    color: #ffffff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
    padding: 5px;

    li {
        width: 100%;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        padding: 5px;
    }
    li:hover {
        background: #05a55d;
    }
}
.divider-center {
    width:5px;
    height: 100vh;
    cursor: ew-resize;
}
.show-box {
    height: 100vh;
    background-color: #f3f3f3
}
.show-top-box {
    height: 50px;
    display: flex;
    gap: 10px;
    align-items: center;
    background-color: #FFFFFF;
    margin-bottom: 5px;
    padding: 0 5px;

    .target-select {
        width: 200px;
        display: flex;
        align-items: center;
        // margin-left: 10px;

        .target-select-title {
            width: 150px;
            font-size: 14px;
            white-space: nowrap;
            margin-right: 5px;
        }
    }
    .translate-select {
        width: 120px;
    }
    .search-box {
        width: 150px;
    }
    .find-button {
        cursor: pointer;
    }
}
.show-Bottom-box {
    height: calc(100% - 55px);
    display: flex;
    padding: 0 0 0 5px;
}
.file-content {
    width: 50%;
    height: 100%;
    white-space: pre-wrap;
    margin-right: 5px;
    overflow-y: auto;
    overflow-x: auto;
}
.file-content::-webkit-scrollbar {
    display: none;
}
.translate-content {
    width: 50%;
    height: 100%;
    overflow-y: auto;
    overflow-x: auto;

    ::v-deep .el-textarea__inner {
        border-radius: 0;
        border: none;
        box-shadow: none;
    }
    ::v-deep .el-textarea__inner:focus {
        box-shadow: none;
    }
    ::v-deep .el-textarea__inner:hover {
        box-shadow: none;
    }
}
.zoom-button {
    width: 50px;
    cursor: pointer;
    color: #07c160;
    z-index: 99;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 5px;
}

::v-deep .search-result {
    background-color: rgba(7, 193, 96, 0.3);
}
::v-deep .current-result {
    background-color: #07c160;
}
::v-deep .highlight {
    background-color: #fee9c8;
}
::v-deep .docx-wrapper {
    background: none !important;
    padding: 0 !important;
    display: inline-block;
    width:100% !important;

    .docx {
        // width: 100% !important;
        box-shadow: none !important;
        margin-bottom: 10px !important;
        width:100% !important;
        padding: 72pt 5pt !important;
    }
    p{
        // margin: 72pt 20pt !important;
    }

}
.right-area{
    margin-left: auto;
    display: flex;
    flex-direction: row;
    margin-right: 10px;
}
</style>

<template>
  <div class="container" ref="containerRef" @mousedown="handleMouseDown" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <!-- 添加一个div，flex布局，里面有两个小div，分别在父div左右两头，左边div是展示文字的，右边div是下拉框选择模型 -->
    <div class="top-bar">
      <div class="left-text">
        {{ sourceAndTargetLang}}
      </div>
      <div class="right-select">
        <el-select v-model="selectedModel" filterable>
          <el-option v-for="item in translateModelList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </div>
    <div v-for="(line, index) in translatedLines" ref="translatedLinesRef" :key="index" class="line" :style="{ color: fontColor, fontSize: fontSize + 'px' }">
      {{ line }}
    </div>
    <!-- <div class="line recognizing" v-if="currentLine" ref="currentLineRef"> -->
    <div class="line" v-if="currentLine" ref="currentLineRef" :style="{ color: fontColor, fontSize: fontSize + 'px' }">
      {{ currentLine }}
    </div>

    <!-- 底部控制按钮栏 -->
    <div class="bottom-bar" v-show="showButtons">

      <div class="icon-wrapper" @click="increaseFontSize">
        <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
          Font Increase
        </q-tooltip>
        <q-icon name="text_increase" style="color: #000;" />
      </div>

      <div class="icon-wrapper" @click="reduceFontSize">
        <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
          Font Decrease
        </q-tooltip>
        <q-icon name="text_decrease" style="color: #000;" />
      </div>
      <div class="icon-wrapper" @click="fontColorChange">
        <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
          Font Color
        </q-tooltip>
        <q-icon name="palette" style="color: #000;" />
      </div>
      <div class="icon-wrapper" @click="increaseOpacity">
        <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
          Opacity Increase
        </q-tooltip>
        <q-icon name="upload" style="color: #000000;" />
      </div>
      <div class="icon-wrapper" @click="reduceOpacity">
        <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
          Opacity Decrease
        </q-tooltip>
        <q-icon name="download" style="color: #000;" />
      </div>
      <div class="icon-wrapper" @click="backgroundColorChange">
        <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
          Bg Color
        </q-tooltip>
        <q-icon name="palette" style="color: #000;" />
      </div>
    </div>
  </div>
</template>
  
  <script setup>
import { ref, onMounted, nextTick, watch } from "vue";
const { ipcRenderer } = require('electron')
import { translateSearch, getTranslateModelList } from "@/api/TranslateApi";
import { Platform } from 'quasar'
const translatedLines = ref([])            // 已翻译的句子
const currentLine = ref('')                // 当前识别中翻译结果
const currentRecognizingText = ref('')     // 当前识别中的原文
const currentRecognizingId = ref(null)     // 当前翻译中的唯一标识
const translateMode = ref(null)
const containerRef = ref(null)
const translatedLinesRef = ref(null)        // 容器引用
const currentLineRef = ref(null)           // 当前行引用
const sourceAndTargetLang = ref(null)
const MIN_DISPLAY_BYTES = 10
const lastRecognizingTextLength = ref(0)
const opacity = ref(0.5)
const fontSize = ref(22) // 默认字体大小
const translatedText = ref('')
const lastTranslateingTextLength = ref(0)
// 控制底部按钮显示/隐藏
const showButtons = ref(false)

// 背景色数组
const backgroundColors = [
  '#FFFFFF', // White
  '#FFFF00', // Yellow
  '#32CD32', // LimeGreen
  '#00FFFF', // Aqua
  '#0000FF', // Blue
  '#FF1493', // DeepPink
  '#FF0000', // Red
  '#000000'  // Black
]
const currentColorIndex = ref(0)
const currentFontColorIndex = ref(0)
const fontColor = ref(backgroundColors[0]) // Add this line to store current font color

const selectedModel = ref(null)
const translateModelList = ref([])

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (currentLineRef.value) {
    currentLineRef.value.scrollIntoView({ behavior: 'smooth' })
  }
}

// 监听翻译行和当前行的变化
watch([translatedLines, currentLine], () => {
  scrollToBottom()
})

onMounted(async () => {
  if (Platform.is.win) {
    showButtons.value = true
  }
  const modelList = await getTranslateModelList()
  translateModelList.value = modelList.data
  selectedModel.value = translateModelList.value[0]?.id || null
  translateMode.value = translateModelList.value.find(item=>item.id == selectedModel.val)
  // translateMode.value = modelList.data.find(item => item.type === 4)
  ipcRenderer.on('realtime-asr-data', (event, res) => {
    // console.log(JSON.parse(res) )
    updateAsrData(JSON.parse(res))
  })
  ipcRenderer.on('stopAsr', (event, res) => {
    translatedText.value = ''
    lastTranslateingTextLength.value = 0
  })
})
watch(selectedModel, (val) => {
  translateMode.value = translateModelList.value.find(item=>item.id == val)
})
//字体颜色调整
const fontColorChange = () => {
  currentFontColorIndex.value = (currentFontColorIndex.value + 1) % backgroundColors.length
  fontColor.value = backgroundColors[currentFontColorIndex.value]
}
//背景色调整
const backgroundColorChange = () => {
  currentColorIndex.value = (currentColorIndex.value + 1) % backgroundColors.length
  containerRef.value.style.backgroundColor = backgroundColors[currentColorIndex.value]
}
//增加透明度
const increaseOpacity = () => {
  if (opacity.value == 1) {
    ipcRenderer.send('change-opacity', 1)
    return
  }
  let value = opacity.value + 0.15
  opacity.value = value
  ipcRenderer.send('change-opacity', value)
}
//减少透明度
const reduceOpacity = () => {
  if (opacity.value <= 0.1) {
    ipcRenderer.send('change-opacity', 0.1)
    return
  }
  let value = opacity.value - 0.15
  opacity.value = value
  ipcRenderer.send('change-opacity', value)
}
//字号变大
const increaseFontSize = () => {
  if (fontSize.value >= 32) return // 设置最大字号
  fontSize.value += 2
}

//字号变小
const reduceFontSize = () => {
  if (fontSize.value <= 12) return // 设置最小字号
  fontSize.value -= 2
}

const getTextBeforeFifthPunctuation = (text) => {
  // 匹配常见中英文标点符号
  const punctuationRegex = /[.,!?，。！？]/g;
  let match;
  let punctuationIndexes = [];

  // 收集所有标点位置
  while ((match = punctuationRegex.exec(text)) !== null) {
    punctuationIndexes.push(match.index);
  }

  // 如果超过10个标点，取前5个标点前的内容（包含第5个标点）
  if (punctuationIndexes.length > 10) {
    const fifthIndex = punctuationIndexes[6]; // 第5个标点的 index
    return text.slice(0, fifthIndex + 1);
  } else {
    return null;
  }
}

const getTextAfterResult = (newText, result) => {
  const index = newText.indexOf(result);
  if (index !== -1) {
    return newText.slice(index + result.length);
  } else {
    return ''; // 如果未找到 result，返回空字符串
  }
}
const getPrimaryLanguageName = (langObj)=> {
  if (!langObj || !langObj.show) return '';
  const parts = langObj.show.split('-');
  return parts.length > 0 ? parts[0] : langObj.show;
}
const updateAsrData = async (data) => {
  const { asrData, lang, langList } = data
  const sourceLangName = getPrimaryLanguageName(lang);
  const targetLangItem = langList.find(item => item.code !== lang.code);
  const targetLangName = getPrimaryLanguageName(targetLangItem);
  sourceAndTargetLang.value = `『${sourceLangName}->${targetLangName}』`;
  let { result: text, type, engine } = asrData
  const targetLang = langList.find(item => item.code !== lang.code)
  console.log('select ',translateMode.value)
  if (!targetLang || !translateMode.value || !text.trim()) return
  console.log(lang,langList)
  // --- 引擎为讯飞，走定制断句逻辑 ---
  if (engine === 'xunfei' && type === 'speechRecognizing') {
    //1.当前文本text，一直去调用翻译接口去翻译
    //2.当text文本中标点符号超过5个的时候，5是个变量，取前5句话，按照第五个标点进行分割，进行一次翻译
    //3.然后当后续text更新时，把第二步已经翻译的5句话排除，剩下的内容进行翻译
    //4.当text内容再累计到5个标点的时候，再重复第二部
    //5.每5句话的翻译后进行一次换行，后续翻译另起一行
    console.log('newText ', text.length)
    console.log('translatedText ', translatedText.value.length)
    if (translatedText.value && translatedText.value.length > 0) {
      let newText = getTextAfterResult(text, translatedText.value)
      text = newText
    }
    console.log('after ', text.length)
    let textBeforeFifthPunctuation = getTextBeforeFifthPunctuation(text)
    if (textBeforeFifthPunctuation) {
      // 翻译前5句话
      translatedText.value += textBeforeFifthPunctuation
      const res = await translateSearch({
        text: textBeforeFifthPunctuation,
        source_lang: lang.code,
        target_lang: targetLang.code,
        translateApi: translateMode.value
      })
      translatedLines.value.push(res.data)
      lastTranslateingTextLength.value = 0
    } else {
      console.log('text length ', text.length)
      console.log('text length lastTranslateingTextLength ', lastTranslateingTextLength.value)
      // 翻译剩余内容
      if (text.length - lastTranslateingTextLength.value < 10) {
        return
      }
      lastTranslateingTextLength.value = text.length
      const res = await translateSearch({
        text: text,
        source_lang: lang.code,
        target_lang: targetLang.code,
        translateApi: translateMode.value
      })
      console.log(res.data)
      currentLine.value = res.data
    }
    return
  }
  if (type === 'speechRecognizing') {
    translatedText.value = ""
    // 若超过展示阈值就进行翻译
    const currentLength = new TextEncoder().encode(text).length
    console.log('currentLength ', currentLength)
    console.log('lastRecognizingTextLength ', lastRecognizingTextLength.value)
    if (currentLength >= MIN_DISPLAY_BYTES && currentLength - lastRecognizingTextLength.value >= MIN_DISPLAY_BYTES) {
      lastRecognizingTextLength.value = currentLength
      // 生成唯一标识，防止多次请求错位
      const currentId = Date.now()
      currentRecognizingId.value = currentId
      currentRecognizingText.value = text

      try {
        const res = await translateSearch({
          text: text,
          source_lang: lang.code,
          target_lang: targetLang.code,
          translateApi: translateMode.value
        })

        // 只更新当前识别项
        if (currentRecognizingId.value === currentId) {
          currentLine.value = res.data
        }
      } catch (err) {
        console.error('speechRecognizing 翻译失败', err)
      }
    }
  }

  if (type === 'speechRecognized') {
    try {
      lastRecognizingTextLength.value = 0
      const res = await translateSearch({
        text: text,
        source_lang: lang.code,
        target_lang: targetLang.code,
        translateApi: translateMode.value
      })

      // 替换当前识别中的展示
      if (currentLine.value) {
        currentLine.value = ''
        currentRecognizingText.value = ''
        currentRecognizingId.value = null
      }

      translatedLines.value.push(res.data)

      // 限制翻译行数（可选）
      if (translatedLines.value.length > 10) {
        translatedLines.value.shift()
      }
    } catch (err) {
      console.error('speechRecognized 翻译失败', err)
    }
  }
}

// 添加窗口拖动相关代码
const handleMouseDown = (e) => {
  // 发送消息给主进程，开始拖动窗口
  ipcRenderer.send('window-drag-start')
}

const handleMouseEnter = () => {
  console.log('mouse entered')
  showButtons.value = true
}

const handleMouseLeave = () => {
  console.log('mouse left')
  if (Platform.is.win) {
    showButtons.value = true
  } else {
    showButtons.value = false
  }
}
  </script>
  
  <style scoped>
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  -webkit-app-region: drag; /* 使整个窗口可拖动 */
}

.container {
  /* padding: 10px; */
  font-size: 22px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 100vh;
  overflow-y: auto;
  background-color: #000000;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  -webkit-app-region: drag;
  color: #fff;
}

.line {
  padding: 0 10px;
  font-size: 22px;
  margin-bottom: 6px;
  /*-webkit-app-region: no-drag; 文本区域不可拖动，保持可选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.recognizing {
  color: gray;
  font-style: italic;
}

/* 底部按钮栏样式 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2px 0;
  display: flex;
  justify-content: flex-end;
  z-index: 100;
  align-items: center;
  background-color: transparent;
  -webkit-app-region: no-drag;
}

.bottom-bar button {
  padding: 5px 10px;
  cursor: pointer;
}

/* 包裹 icon 的正方形 div */
.icon-wrapper {
  width: 35px;
  height: 30px;
  display: flex;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
  background-color: #aba9ab;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  -webkit-app-region: no-drag;
}

.icon-wrapper:last-child {
  margin-right: 15px;
}

.icon-wrapper:hover {
  background-color: #888888;
  transform: scale(1.05);
}

.icon-wrapper:active {
  background-color: #666666;
  transform: scale(0.95);
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  -webkit-app-region: no-drag;
  color: #fff;
}
.left-text {
  font-size: 20px;
  font-weight: bold;
  color: #aba9ab;
}
.right-select {
  margin-right: 10px;
  width: 100px;
}
</style>
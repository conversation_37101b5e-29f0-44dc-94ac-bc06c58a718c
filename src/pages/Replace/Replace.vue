<template>
    <!--  替换维护页面-->
    <el-dialog
        v-model="replaceVisibleLocal"
        center
        fullscreen
        :show-close="false"
        :before-close="beforeClose"
        @dragenter="handleDragEnter"
    >
        <template #header="{ titleId, titleClass}">
            <el-row>
                <el-col :xs="2" :sm="2" :md="2" :lg="2" :xl="1">
                    <div class="back" @click="cancelReplace"> &lt; Back</div>
                </el-col>
                <el-col :xs="22" :sm="22" :md="22" :lg="22" :xl="22">
                    <div :id="titleId" :class="titleClass">{{ `Force Replace(${editReplaceScene.name})` }}</div>
                </el-col>
            </el-row>
        </template>
        <div class="vocabulary">
            <el-row>
                <div class="buttonBox">
                    <el-input v-model="searchValue" clearable @input="handelSearch" placeholder="Search Info" style="margin-right: 12px"></el-input>
                    <el-button type="default" @click="handelSearch">Search</el-button>
                    <el-button type="primary" @click="handleReplaceAdd">Add</el-button>
                    <el-upload
                        :action='url'
                        class="upload"
                        accept=".xlsx, .xls"
                        :show-file-list="false"
                        :multiple="false"
                        :on-success="afterUpload"
                        :on-error="afterUpload"
                    >
                        <el-button type="primary">Upload</el-button>
                    </el-upload>
                    <el-button type="primary" @click="downloadReplace">Download</el-button>
                </div>
            </el-row>
            <el-row>
                <el-table v-loading="loading" :data="replaceData" border>
                    <el-table-column type="index" label="Index" width="100" align="center"/>
                    <el-table-column sortable prop="replaceA" label="Source" align="center"/>
                    <el-table-column prop="replaceB" label="Target" align="center"/>
                    <el-table-column sortable prop="createAt" label="Create Date" align="center"/>
                    <el-table-column label="Operate" width="200" align="center">
                        <template #default="scope">
                            <el-button
                                @click="editReplaceForm(scope.row)"
                                size="small"
                                type="default"
                            >Edit
                            </el-button>
                            <el-popconfirm width="220px" title="Are you sure to delete?" @confirm="deleteReplaceForm(scope.row)">
                                <template #reference>
                                    <el-button
                                        type="danger"
                                        size="small"
                                    >Delete
                                    </el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <div class="pagination">
                <el-pagination layout="prev, pager, next" :total="total" :current-page="currentPage"
                               @current-change="handelChangeCurrent"/>
            </div>
        </div>
    </el-dialog>

    <!--  替换新增、编辑页面-->
    <el-dialog
        v-model="replaceAddVisible"
        :title="replaceAddForm.id !== undefined ? 'Edit Force Replace' : 'Add Force Replace'"
        center
        destroy-on-close
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        width="450"
        @keydown.enter="saveReplaceForm(replaceRef)"
    >
        <el-form ref="replaceRef" :model="replaceAddForm" :rules="addRules">
            <el-row justify="center">
                <el-col :span="20">
                    <el-form-item label="A" prop="replaceA" label-width="40">
                        <el-input v-model="replaceAddForm.replaceA"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row justify="center">
                <el-col :span="20">
                    <el-form-item label="B" prop="replaceB" label-width="40">
                        <el-input v-model="replaceAddForm.replaceB"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row justify="end">
                <el-col :span="7">
                    <el-button type="default" @click="cancelReplaceForm">Cancel</el-button>
                </el-col>
                <el-col :span="4">
                    <el-button type="primary" @click="saveReplaceForm(replaceRef)">Save</el-button>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>

    <DragUpload
        v-model:uploading="uploading"
        currentScene="replace"
        :scene-id="editReplaceScene.id"
        :lang-list="langList"
        :afterUpload="handleAfterUpload"
    />
</template>

<script setup>
import {reactive, ref, watch} from "vue";
import {ElMessage} from "element-plus";
import {addReplace, batchAddReplace, deleteReplace, downReplace, getReplace} from "@/api/ReplaceApi";
import {readExcelToJson} from "@/utils/xlsxUtil";
import DragUpload from "@/pages/components/DragUpload.vue";

// 替换数据集
const replaceData = ref([])
// 替换新增表单数据
const replaceAddForm = ref({})
const replaceVisibleLocal = ref(false)
const replaceAddVisible = ref(false)
const replaceRef = ref()
// 当前页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchValue = ref()
const addRules = reactive({
    replaceA: [
        {required: true, message: 'Please Enter Target', trigger: 'blur'},
    ],
    replaceB: [
        {required: true, message: 'Please Enter Content', trigger: 'blur'},
    ]
})
const url = ref('')
const Ashow = ref()
const Bshow = ref()
const loading = ref(false)
const uploading = ref(false) //是否展示拖拽上传

const props = defineProps({
    replaceVisible: {type: Boolean},
    cancelReplace: {type: Function},
    editReplaceScene: {type: Object},
    langList: {type: Array},
    replaceSceneId: {type: Number},
    // 用于当替换页新增、修改、删除、上传的时候，检测是否为已选择的场景摧毁
    // 如果是的话，需要实时更新replaceRes，来去更新翻译数据集
    checkIsReplaceScene: {type: Function}
})

watch(
    () => props.replaceVisible,
    (newValue, oldValue) => {
        replaceVisibleLocal.value = newValue

        if(process.env.NODE_ENV=='development'){
            url.value = `/conf/asr/replaces/import?userId=${localStorage.getItem('userData') ? JSON.parse(localStorage.getItem('userData')).userId : ''}&sceneId=${props.editReplaceScene.id}`
        }else{
            url.value = `https://boothapi.gtmeeting.com/conf/asr/replaces/import?userId=${localStorage.getItem('userData') ? JSON.parse(localStorage.getItem('userData')).userId : ''}&sceneId=${props.editReplaceScene.id}`
        }
        getReplaceData()
    }
)
watch(
    () => props.langList,
    (newValue, oldValue) => {
        newValue.forEach(item => {
            if (item.main) {
                Ashow.value = `A(${item.show})`
            } else {
                Bshow.value = `B(${item.show})`
            }
        })
    }
)
// 搜索按钮的方法
const handelSearch = () => {
    currentPage.value = 1
    getReplaceData()
}
// 分页获取替换数据的接口
const getReplaceData = () => {
    loading.value = true
    getReplace({
        startPage: currentPage.value - 1,
        pageNum: pageSize.value,
        searchInfo: searchValue.value,
        sceneId: props.editReplaceScene.id
    }).then(res => {
        replaceData.value = res.data.data
        total.value = res.data.count
        loading.value = false
    })
}
// 切换页数
const handelChangeCurrent = (number) => {
    currentPage.value = number
    getReplaceData()
}
// 上传后的方法
const afterUpload = res => {
    if (res.success) {
        ElMessage.success('Upload Success!');
        getReplaceData()
        props.checkIsReplaceScene(props.editReplaceScene.id)
        return
    }
    ElMessage.error(res.message)
}
// 下载的方法
const downloadReplace = () => {
    downReplace({sceneId: props.editReplaceScene.id})
}
// 打开替换新增编辑页面
const handleReplaceAdd = () => {
    replaceAddVisible.value = true
}
// 替换新增、编辑页面保存的方法
const saveReplaceForm = (replaceRef) => {
    replaceRef.validate(valid => {
        if (valid) {
            addReplace({...replaceAddForm.value, sceneId: props.editReplaceScene.id}).then(res => {
                if (res.success) {
                    ElMessage.success('Add Success!')
                    cancelReplaceForm()
                    getReplaceData()
                    props.checkIsReplaceScene(props.editReplaceScene.id)
                } else {
                    ElMessage.error(res.msg)
                }
            })
        }
    })
}
// 新增、编辑页面取消的方法
const cancelReplaceForm = () => {
    replaceAddForm.value = {}
    replaceAddVisible.value = false;
}
// 打开编辑页面的方法
const editReplaceForm = (item) => {
    replaceAddForm.value = {...item}
    replaceAddVisible.value = true
}
// 删除的接口
const deleteReplaceForm = (delItem) => {
    deleteReplace({ids: [delItem.id]}).then(res => {
        if (res.success) {
            getReplaceData()
            ElMessage.success('Delete Success!')
            props.checkIsReplaceScene(props.editReplaceScene.id)
        } else {
            ElMessage.error(res.msg)
        }
    })
}
// 结束前的回调，当点击ESC返回时，主动去修改visible的值
const beforeClose = done => {
    props.cancelReplace()
    done()
}
// 拖拽文件进来窗口触发的操作
const handleDragEnter = event => {
    event.preventDefault();
    uploading.value = true
}
// 拖拽上传完成后的操作
const handleAfterUpload = () => {
    getReplaceData()
    props.checkIsReplaceScene(props.editReplaceScene.id)
}
</script>

<style lang="scss" scoped>
.back {
    cursor: pointer;
    color: #07c160;
}

.buttonBox {
    display: flex;
    margin-bottom: 10px;

    .upload {
        margin: 0 12px;
    }
}

.pagination {
    margin-top: 10px;
    display: flex;
    justify-content: end;
}
</style>

<template>
    <!--  替换场景-->
    <el-dialog
        v-model="replaceSceneVisibleLocal"
        center
        fullscreen
        :show-close="false"
        :before-close="beforeClose"
        @dragenter="handleDragEnter"
    >
        <template #header="{ titleId, titleClass}">
            <el-row>
                <el-col :xs="2" :sm="2" :md="2" :lg="2" :xl="1">
                    <div class="back" @click="closeReplaceScene"> &lt; Back</div>
                </el-col>
                <el-col :xs="22" :sm="22" :md="22" :lg="22" :xl="22">
                    <div :id="titleId" :class="titleClass">Force Replace List</div>
                </el-col>
            </el-row>
        </template>
        <div class="replaceScene">
            <el-row class="buttonBox" :gutter="10">
                <el-col :span="4">
                    <el-input v-model="searchValue" clearable @input="handelSearchReplaceScene" placeholder="Search Info"></el-input>
                </el-col>
                <el-col :span="6">
                    <div>
                        <el-button type="default" @click="handelSearchReplaceScene">Search</el-button>
                        <el-button type="primary" @click="handleReplaceSceneAdd">Add</el-button>
                    </div>
                </el-col>
                <el-col :span="14">
                    <div style="display: flex;">
                        <div style="width: 120px;display: flex;justify-content:center;align-items: center">Choose List:</div>
                        <el-select v-model="replaceSceneIdLocal" placeholder="Select"
                                   @change="changeReplaceScene">
                            <el-option
                                v-for="item in allReplaceSceneData"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </div>
                </el-col>
            </el-row>
            <el-row>
                <el-table v-loading="loading" :data="replaceSceneData" border>
                    <el-table-column type="index" label="Index" width="100" align="center"/>
                    <el-table-column prop="name" label="Name" align="center"/>
                    <el-table-column label="Operate" width="300" align="center">
                        <template #default="scope">
                            <el-button
                                @click="openReplace(scope.row)"
                                size="small"
                                type="default"
                            >Management
                            </el-button>
                            <el-button
                                @click="editReplaceSceneForm(scope.row)"
                                size="small"
                                type="default"
                            >Edit
                            </el-button>
                            <el-popconfirm width="220px" title="Are you sure to delete?" @confirm="deleteReplaceSceneForm(scope.row)">
                                <template #reference>
                                    <el-button
                                        type="danger"
                                        size="small"
                                    >Delete
                                    </el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <div class="pagination">
                <el-pagination layout="prev, pager, next" :total="total" :current-page="currentPage"
                               @current-change="handelChangeCurrent"/>
            </div>
        </div>
    </el-dialog>
    <!--  替换场景新增编辑页面-->
    <el-dialog
        v-model="replaceSceneAddVisible"
        :title="replaceSceneAddForm.id !== undefined ? 'Edit Vocabulary Scene' : 'Add Vocabulary Scene'"
        center
        destroy-on-close
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        width="350"
        @keydown.enter="saveReplaceSceneForm(replaceSceneRef)"
    >
        <el-form ref="replaceSceneRef" :model="replaceSceneAddForm" :rules="addRules">
            <el-row justify="center">
                <el-col :span="20">
                    <el-form-item label="Name" prop="name" label-width="30">
                        <el-input v-model="replaceSceneAddForm.name"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row justify="end">
                <el-col :span="7">
                    <el-button type="default" @click="cancelReplaceSceneForm">Cancel</el-button>
                </el-col>
                <el-col :span="4">
                    <el-button type="primary" @click="saveReplaceSceneForm(replaceSceneRef)">Save</el-button>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>

    <!-- 替换页面 -->
    <Replace
        :replaceVisible="replaceVisible"
        :cancelReplace="cancelReplace"
        :editReplaceScene="editReplaceScene"
        :langList="langList"
        :replaceSceneId="replaceSceneIdLocal"
        :checkIsReplaceScene="checkIsReplaceScene"
    />

    <!-- 拖拽上传 -->
    <DragUpload
        v-model:uploading="uploading"
        currentScene="replace"
        :lang-list="langList"
        :all-glossary-scene-data="allReplaceSceneData"
        :afterAddScene="handleAfterAddScene"
        :after-upload="handleAfterAddScene"
    />
</template>

<script setup>
import {onMounted, reactive, ref, watch} from "vue";
import {
    addReplaceScene,
    deleteReplaceScene,
    getAllReplaceScene,
    getReplaceScene
} from "@/api/ReplaceApi";
import {ElMessage} from "element-plus";
import Replace from "@/pages/Replace/Replace.vue";
import DragUpload from "@/pages/components/DragUpload.vue";

const replaceSceneRef = ref()
const replaceSceneData = ref([])
const allReplaceSceneData = ref([])
const replaceSceneAddForm = ref({})
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchValue = ref()
const replaceSceneAddVisible = ref(false)
const addRules = reactive({
    name: [
        {required: true, message: 'Please Enter Scene Name', trigger: 'blur'},
    ]
})
// 用户选择的当前翻译的替换场景
const replaceSceneIdLocal = ref()
// 用户正在编辑的替换场景条目
const editReplaceScene = ref({})
// 是否展示替换页面
const replaceVisible = ref(false)
// 是否展示替换场景页面
const replaceSceneVisibleLocal = ref(false)
const loading = ref(false)
// 拖拽上传中反馈展示
const uploading = ref(false)

const props = defineProps({
    // 替换场景页面是否展示
    replaceSceneVisible: {
        type: Boolean
    },
    // 关闭替换场景页面的方法
    closeReplaceScene: {
        type: Function
    },
    // 当前语言列表
    langList: {
        type: Array
    },
    // 用户选择的当前翻译的替换场景
    replaceSceneId: {
        type: Number
    },
    // 改变用户选择替换场景的方法
    changeReplaceScene: {
        type: Function
    },
    // 用于当替换页新增、修改、删除、上传的时候，检测是否为已选择的场景摧毁
    // 如果是的话，需要实时更新replaceRes，来去更新翻译数据集
    checkIsReplaceScene: {
        type: Function
    },
    clearReplaceSceneId: {
        type: Function
    }
})

watch(
    () => props.replaceSceneId,
    (newValue, oldValue) => {
        replaceSceneIdLocal.value = newValue
    }
)
watch(
    () => props.replaceSceneVisible,
    (nValue, oValue) => {
        replaceSceneVisibleLocal.value = nValue
        if (nValue) {
            getReplaceSceneData()
            getAllReplaceSceneData()
        }
    }
)
// 获取表格分页数据
const getReplaceSceneData = () => {
    loading.value = true
    getReplaceScene({
        startPage: currentPage.value - 1,
        pageNum: pageSize.value,
        searchInfo: searchValue.value
    }).then(res => {
        replaceSceneData.value = res.data.data
        total.value = res.data.count
        loading.value = false
    })
}
// 获取所有场景，用来选择场景
const getAllReplaceSceneData = () => {
    getAllReplaceScene({}).then(res => {
        allReplaceSceneData.value = [{id: null, name: 'None'}, ...res.data]
    })
}
// 搜索框的查询接口
const handelSearchReplaceScene = () => {
    currentPage.value = 1
    getReplaceSceneData()
}
// 展示新增框
const handleReplaceSceneAdd = () => {
    replaceSceneAddVisible.value = true
}
// 取消新增框
const cancelReplaceSceneForm = () => {
    replaceSceneAddForm.value = {}
    replaceSceneAddVisible.value = false;
}
// 新增和编辑场景条目的方法
const saveReplaceSceneForm = (replaceSceneRef) => {
    replaceSceneRef.validate(valid => {
        if (valid) {
            addReplaceScene(replaceSceneAddForm.value).then(res => {
                if (res.success) {
                    ElMessage.success('Add Success!')
                    cancelReplaceSceneForm()
                    getReplaceSceneData()
                    getAllReplaceSceneData()
                } else {
                    ElMessage.error(res.msg)
                }
            })
        }
    })
}
// 打开编辑框的方法
const editReplaceSceneForm = (item) => {
    replaceSceneAddForm.value = {...item}
    replaceSceneAddVisible.value = true
}
// 删除场景的方法
const deleteReplaceSceneForm = (delItem) => {
    deleteReplaceScene(delItem.id).then(res => {
        if (res.success) {
            getReplaceSceneData()
            getAllReplaceSceneData()
            ElMessage.success('Delete Success!')
            if (delItem.id === replaceSceneIdLocal.value) {
                props.clearReplaceSceneId()
            }
        } else {
            ElMessage.error(res.msg)
        }
    })
}
// 切换页数
const handelChangeCurrent = (number) => {
    currentPage.value = number
    getReplaceSceneData()
}
// 打开替换页面的方法
const openReplace = (item) => {
    editReplaceScene.value = item
    replaceVisible.value = true
}
// 关闭替换页面的方法
const cancelReplace = () => {
    replaceVisible.value = false
}
// 结束前的回调，当点击ESC返回时，主动去修改visible的值
const beforeClose = done => {
    props.closeReplaceScene()
    done()
}
// 拖拽文件进来窗口触发的操作
const handleDragEnter = event => {
    event.preventDefault();
    uploading.value = true
}
// 导入新增场景名称后刷新数据
const handleAfterAddScene = () => {
    getReplaceSceneData()
    getAllReplaceSceneData()
}
</script>

<style lang="scss" scoped>
.back {
    cursor: pointer;
    color: #07c160;
}

.buttonBox {
    margin-bottom: 10px;
}

.pagination {
    margin-top: 10px;
    display: flex;
    justify-content: end;
}
</style>


<template>
  <div class="container"  ref="containerRef" @mousedown="handleMouseDown" @mouseup="handleMouseUp" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <!-- 底部控制按钮栏 -->
    <div class="top-bar" :class="{ 'top-css': Platform.is.win }">
      <div class="left-text">
        {{ sourceAndTargetLang}}
      </div>
      <div class="right-controls">
        <el-select v-model="selectedModel" popper-class="mode-select" filterable>
          <el-option v-for="item in translateModelList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <div class="icon-wrapper" @click="increaseFontSize">
          <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
            Font Increase
          </q-tooltip>
          <q-icon name="text_increase" style="color: #000;" />
        </div>
        <div class="icon-wrapper" @click="reduceFontSize">
          <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
            Font Decrease
          </q-tooltip>
          <q-icon name="text_decrease" style="color: #000;" />
        </div>
        <div class="icon-wrapper" @click="fontColorChange">
          <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
            Font Color
          </q-tooltip>
          <q-icon name="palette" style="color: #000;" />
        </div>
        <div class="icon-wrapper" @click="increaseOpacity">
          <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
            Opacity Increase
          </q-tooltip>
          <q-icon name="upload" style="color: #000000;" />
        </div>
        <div class="icon-wrapper" @click="reduceOpacity">
          <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
            Opacity Decrease
          </q-tooltip>
          <q-icon name="download" style="color: #000;" />
        </div>
        <div class="icon-wrapper" @click="backgroundColorChange">
          <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
            Bg Color
          </q-tooltip>
          <q-icon name="palette" style="color: #000;" />
        </div>
        <div class="icon-wrapper" v-if="!stayTop">
          <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
          Always Top
          </q-tooltip>
          <q-icon  name="vertical_align_top" style="color: #000000;"  @click="alwaysOnTopClick" />
        </div>
        <div class="icon-wrapper bg-primary" v-if="stayTop">
          <q-tooltip class="text-black bg-white text-bold" anchor="bottom middle">
          Cancel Always Top
          </q-tooltip>
          <q-icon  name="vertical_align_top" style="color: #ffffff;" @click="alwaysOnTopClick" />
        </div>
        
      </div>
    </div>
    <div class="content" :class="{ 'drag-css': Platform.is.mac }" @scroll="handleItemScroll($event, null)" @wheel="handleItemWheel($event, null)">
      <div v-for="(line, index) in translatedLines" v-show="!line.hide" ref="translatedLinesRef" :key="index" class="line" :style="{ color: fontColor, fontSize: fontSize + 'px' }">
        {{ line.text }}
      </div>
      <!-- <div class="line recognizing" v-if="currentLine" ref="currentLineRef"> -->
      <div class="line" v-if="currentLine" ref="currentLineRef" :style="{ color: fontColor, fontSize: fontSize + 'px' }">
        {{ currentLine }}
      </div>
    </div>
    <!-- 添加浮动箭头按钮 -->
    <div v-show="showScrollButton" class="scroll-to-bottom" @click="scrollBtnClick">
            <el-icon :size="20"><ArrowDown /></el-icon>
      </div>
  </div>
</template>
  
  <script setup>
import { ref, onMounted, nextTick, watch } from "vue";
const { ipcRenderer } = require('electron')
import { translateSearch, getTranslateModelList } from "@/api/TranslateApi";
import { Platform } from 'quasar'
import {getTextAfterResult,getTextBeforeFifthPunctuation} from "@/utils/commonUtils";
import { convertLanguageCodes } from "@/utils/languageCodeConverter";
const translatedLines = ref([])            // 已翻译的句子
const currentLine = ref('')                // 当前识别中翻译结果
const currentRecognizingText = ref('')     // 当前识别中的原文
const currentRecognizingId = ref(null)     // 当前翻译中的唯一标识
const translateMode = ref(null)
const containerRef = ref(null)
const translatedLinesRef = ref(null)        // 容器引用
const currentLineRef = ref(null)           // 当前行引用
const sourceAndTargetLang = ref(null)
const MIN_DISPLAY_BYTES = 10
const lastRecognizingTextLength = ref(0)
const opacity = ref(0.5)
const fontSize = ref(22) // 默认字体大小
const translatedText = ref('')
const lastTranslateingTextLength = ref(0)
// 控制底部按钮显示/隐藏
const showButtons = ref(false)
const stayTop = ref(false)
const showScrollButton = ref(false)
// 背景色数组
const backgroundColors = [
  '#FFFFFF', // White
  '#FFFF00', // Yellow
  '#32CD32', // LimeGreen
  '#00FFFF', // Aqua
  '#0000FF', // Blue
  '#FF1493', // DeepPink
  '#FF0000', // Red
  '#000000'  // Black
]
const currentColorIndex = ref(0)
const currentFontColorIndex = ref(0)
const fontColor = ref(backgroundColors[0]) // Add this line to store current font color

const selectedModel = ref(null)
const translateModelList = ref([])
const speakerArr = ref([])

// 添加自动滚动控制状态
const autoScrollEnabled = ref(true)

// 从localStorage加载用户设置
const loadUserPreferences = () => {
  const savedFontSize = localStorage.getItem('translateFontSize')
  const savedFontColor = localStorage.getItem('translateFontColor')
  const savedBackgroundColor = localStorage.getItem('translateBackgroundColor')
  const savedOpacity = localStorage.getItem('translateOpacity')
  const savedColorIndex = localStorage.getItem('translateColorIndex')
  const savedFontColorIndex = localStorage.getItem('translateFontColorIndex')
  const savedModel = localStorage.getItem('translateModel')

  if (savedFontSize) fontSize.value = parseInt(savedFontSize)
  if (savedFontColor) fontColor.value = savedFontColor
  if (savedBackgroundColor) containerRef.value.style.backgroundColor = savedBackgroundColor
  if (savedOpacity) {
    opacity.value = parseFloat(savedOpacity)
    ipcRenderer.send('change-opacity', opacity.value)
  }
  if (savedColorIndex) currentColorIndex.value = parseInt(savedColorIndex)
  if (savedFontColorIndex) currentFontColorIndex.value = parseInt(savedFontColorIndex)
  if (savedModel) {
    selectedModel.value = +savedModel
  }
}

// 保存用户设置到localStorage
const saveUserPreferences = () => {
  localStorage.setItem('translateFontSize', fontSize.value.toString())
  localStorage.setItem('translateFontColor', fontColor.value)
  localStorage.setItem('translateBackgroundColor', containerRef.value.style.backgroundColor)
  localStorage.setItem('translateOpacity', opacity.value.toString())
  localStorage.setItem('translateColorIndex', currentColorIndex.value.toString())
  localStorage.setItem('translateFontColorIndex', currentFontColorIndex.value.toString())
  localStorage.setItem('translateModel', selectedModel.value)
}

// 修改滚动到底部的函数
const scrollToBottom = async () => {
  if (!autoScrollEnabled.value) return  // 交传模式下禁用自动滚动
  
  await nextTick()
  if(showScrollButton.value) return
  const content = document.querySelector('.content')
  if (content) {
    content.scrollTop = content.scrollHeight
  }
}

// 监听翻译行和当前行的变化
watch([translatedLines, currentLine], () => {
  scrollToBottom()
})

const scrollBtnClick = ()=>{
    scrollToBottom()
    showScrollButton.value = false
}

// 滚动步长
const scrollStep = 100

// 向下滚动方法
const scrollDown = () => {
    const content = document.querySelector('.content')
    if (content) {
        const currentScrollTop = content.scrollTop
        const newScrollTop = currentScrollTop + scrollStep
        console.log('realtimeTranslate scrollDown from', currentScrollTop, 'to', newScrollTop)
        content.scrollTo({
            top: newScrollTop,
            behavior: 'smooth'
        })
    }
}

// 向上滚动方法
const scrollUp = () => {
    const content = document.querySelector('.content')
    if (content) {
        const currentScrollTop = content.scrollTop
        const newScrollTop = Math.max(0, currentScrollTop - scrollStep)
        console.log('realtimeTranslate scrollUp from', currentScrollTop, 'to', newScrollTop)
        content.scrollTo({
            top: newScrollTop,
            behavior: 'smooth'
        })
    }
}

onMounted(async () => {
  if (Platform.is.win) {
    showButtons.value = true
  }
  const modelList = await getTranslateModelList()
  translateModelList.value = modelList.data
  
  // 检查保存的模型是否在可用模型列表中
  if (selectedModel.value) {
    const modelExists = translateModelList.value.some(item => item.id === selectedModel.value)
    if (!modelExists) {
      selectedModel.value = translateModelList.value[0]?.id || null
    }
  } else {
    selectedModel.value = translateModelList.value[0]?.id || null
  }

  translateMode.value = translateModelList.value.find(item=>item.id == selectedModel.value)
  // 加载用户设置
  loadUserPreferences()
  ipcRenderer.on('realtime-asr-data', (event, res) => {
    updateAsrData(JSON.parse(res))
    updateTextVisibilityBySpeaker(speakerArr.value)
  })
  ipcRenderer.on('hide-speaker-change', (event, res) => {
    // console.log('hide-speaker-change', JSON.parse(res))
    speakerArr.value = JSON.parse(res)
    updateTextVisibilityBySpeaker(speakerArr.value)
  })
  // 根据speakerArr对文本进行显示或隐藏处理
const updateTextVisibilityBySpeaker = (speakerArr) => {
    //这里要参考Translation隐藏文本的逻辑，对translatedLines中的数据进行隐藏，只是修改hide为true, hide为true则设置display为none
    translatedLines.value.forEach(item => {
        const prefix = speakerArr.find(speaker => item.text.startsWith(speaker.name))
        if (prefix && prefix.hide) {
            item.hide = true
        } else {
            item.hide = false
        }
    })
    
}
  ipcRenderer.on('stopAsr', (event, res) => {
    translatedText.value = ''
    lastTranslateingTextLength.value = 0
  })

  // 监听来自Translation窗口的滚动同步事件
  ipcRenderer.on('scroll-sync', (event, { direction, step }) => {
    console.log('realtimeTranslate received scroll-sync:', direction, step)
    if (direction === 'down') {
      scrollDown()
    } else if (direction === 'up') {
      scrollUp()
    }
  })
})
watch(selectedModel, (val) => {
  translateMode.value = translateModelList.value.find(item=>item.id == val)
  saveUserPreferences() // 当模型改变时保存设置
})
const handleItemScroll = (event, itemId) => {
    const target = event.target;
    // 检查是否滚动到底部
    const height = Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight);
    if (height < 1) {
        showScrollButton.value = false;
    }
}
const handleItemWheel = (event, itemId) => {
    if (event.deltaY < 0) {
        showScrollButton.value = true;
    } 
}
//字体颜色调整
const fontColorChange = () => {
  currentFontColorIndex.value = (currentFontColorIndex.value + 1) % backgroundColors.length
  fontColor.value = backgroundColors[currentFontColorIndex.value]
  saveUserPreferences()
}
//背景色调整
const backgroundColorChange = () => {
  currentColorIndex.value = (currentColorIndex.value + 1) % backgroundColors.length
  containerRef.value.style.backgroundColor = backgroundColors[currentColorIndex.value]
  saveUserPreferences()
}
//置顶
const alwaysOnTopClick = ()=>{
  stayTop.value = !stayTop.value
  ipcRenderer.send("translateWindowStaytopClick",stayTop.value);
}
//关闭窗口
const closeTranslateWindow = ()=>{
  ipcRenderer.send("closeTranslateWindow",stayTop.value);
}
//增加透明度
const increaseOpacity = () => {
  if (opacity.value == 1) {
    ipcRenderer.send('change-opacity', 1)
    return
  }
  let value = opacity.value + 0.15
  opacity.value = value
  ipcRenderer.send('change-opacity', value)
  saveUserPreferences()
}
//减少透明度
const reduceOpacity = () => {
  if (opacity.value <= 0.1) {
    ipcRenderer.send('change-opacity', 0.1)
    return
  }
  let value = opacity.value - 0.15
  opacity.value = value
  ipcRenderer.send('change-opacity', value)
  saveUserPreferences()
}
//字号变大
const increaseFontSize = () => {
  if (fontSize.value >= 32) return // 设置最大字号
  fontSize.value += 2
  saveUserPreferences()
}

//字号变小
const reduceFontSize = () => {
  if (fontSize.value <= 12) return // 设置最小字号
  fontSize.value -= 2
  saveUserPreferences()
}


const getPrimaryLanguageName = (langObj)=> {
  if (!langObj || !langObj.show) return '';
  const parts = langObj.show.split('-');
  return parts.length > 0 ? parts[0] : langObj.show;
}
const updateAsrData = async (data) => {
  let { asrData, lang, langList, openConsecutive } = data
  console.log('langList ', langList, 'lang ',lang)
  console.log('## asrData ', asrData)
  // 语言代码转换
  const convertedLangCodes = convertLanguageCodes(langList)
  console.log('转换后的语言代码数组:', convertedLangCodes)
  
  // 根据交传模式控制自动滚动
  autoScrollEnabled.value = !openConsecutive
  let sourceLangName = getPrimaryLanguageName(lang);
  let targetLangItem = langList.find(item => item.code !== lang.code);
  let targetLangName = getPrimaryLanguageName(targetLangItem);
  let targetLang = langList.find(item => item.code !== lang.code)
  let { result: text, type, engine,speaker } = asrData
  
  if(asrData.source == 'azure'){
    lang = langList.find(item => item.code == asrData.languageCode)
    sourceLangName = getPrimaryLanguageName(lang);
    targetLang = langList.find(item => item.code !== lang.code && item.code !== "Auto");
    targetLangName = getPrimaryLanguageName(targetLang);
  }
  //讯飞Auto模式下进行判断
  if(asrData.source == 'xunfei' && lang.code == 'Auto'){
      let asrInfo = asrData.result
      // 判断文本是中文还是英文
      const chineseRegex = /[\u4e00-\u9fff]/g; // 匹配中文字符，使用全局匹配
      const englishRegex = /[a-zA-Z]/g; // 匹配英文字符，使用全局匹配

      const chineseMatches = asrInfo.match(chineseRegex) || [];
      const englishMatches = asrInfo.match(englishRegex) || [];

      const chineseCount = chineseMatches.length;
      const englishCount = englishMatches.length;

      let language = '';
      if (chineseCount > 0 && englishCount === 0) {
        language = 'zh-CN';
      } else if (englishCount > 0 && chineseCount === 0) {
        language = 'en-US';
      } else if (chineseCount > 0 && englishCount > 0) {
        // 中英文混合，判断哪个多
        if (chineseCount > englishCount) {
          language = 'zh-CN'; // 中文占主导
        } else if (englishCount > chineseCount) {
          language = 'en-US'; // 英文占主导
        } else {
          language = 'zh-CN'; // 数量相等时默认中文
        }
      } else {
        language = 'zh-CN'; // 无法识别或其他语言
      }
      lang = langList.find(item => item.code == language)
      sourceLangName = getPrimaryLanguageName(lang);
      targetLang = langList.find(item => item.code !== lang.code && item.code !== "Auto");
      targetLangName = getPrimaryLanguageName(targetLang);
  }
  // 使用转换后的语言代码进行语言检测
  // let detectlang_res = await ipcRenderer.invoke('detectTextLanguage', text, convertedLangCodes)
  // console.log('detectlang_res ', detectlang_res)
  // if(detectlang_res.success){
  //   let sourceLang = detectlang_res.data.dominant.language
  //   langList.forEach(item=>{
  //     if(item.code.startsWith(sourceLang)){
  //       //console.log('---sourceLangName ',getPrimaryLanguageName(item))
  //       lang = item
  //       sourceLangName = getPrimaryLanguageName(lang);
  //     }
  //     if(!item.code.startsWith(sourceLang) && item.code !== 'Auto'){
  //       //console.log('---targetLangName ',getPrimaryLanguageName(item))
  //       targetLang = item
  //       targetLangName = getPrimaryLanguageName(targetLang);
  //     }
  //   })
  // }else if (lang.code === 'Auto') {
  //   // 检测失败，且 source 是 Auto，手动设置一个默认 sourceLang
  //   const fallbackLang = langList.find(item => item.code !== 'Auto');
  //   if (fallbackLang) {
  //     lang = fallbackLang;
  //     sourceLangName = getPrimaryLanguageName(fallbackLang);

  //     // 设置另一个作为 targetLang
  //     const otherLang = langList.find(item => item.code !== 'Auto' && item.code !== fallbackLang.code);
  //     if (otherLang) {
  //       targetLang = otherLang;
  //       targetLangName = getPrimaryLanguageName(otherLang);
  //     }
  //   }
  // }
  sourceAndTargetLang.value = `${sourceLangName}->${targetLangName}`;
  if(!speaker){
    speaker = ""
  }
  text = text.replace(/\r?\n$/, '');
  console.log('select ',translateMode.value)
  if (!targetLang || !translateMode.value || !text.trim()) return
  console.log('source ',lang.code, ' target ',targetLang.code)
  // --- 引擎为讯飞，走定制断句逻辑 ---
  if (engine === 'xunfei' && type === 'speechRecognizing') {
    //1.当前文本text，一直去调用翻译接口去翻译
    //2.当text文本中标点符号超过5个的时候，5是个变量，取前5句话，按照第五个标点进行分割，进行一次翻译
    //3.然后当后续text更新时，把第二步已经翻译的5句话排除，剩下的内容进行翻译
    //4.当text内容再累计到5个标点的时候，再重复第二部
    //5.每5句话的翻译后进行一次换行，后续翻译另起一行
    console.log('newText ', text.length)
    console.log('translatedText ', translatedText.value.length)
    if (translatedText.value && translatedText.value.length > 0) {
      let newText = getTextAfterResult(text, translatedText.value)
      text = newText
    }
    console.log('after ', text.length)
    let textBeforeFifthPunctuation = getTextBeforeFifthPunctuation(text)
    if (textBeforeFifthPunctuation) {
      // 翻译前5句话
      translatedText.value += textBeforeFifthPunctuation
      const res = await translateSearch({
        text: textBeforeFifthPunctuation,
        source_lang: lang.code,
        target_lang: targetLang.code,
        translateApi: translateMode.value
      })
      translatedLines.value.push({text:res.data,hide:false})
      lastTranslateingTextLength.value = 0
    } else {
      console.log('text length ', text.length)
      console.log('text length lastTranslateingTextLength ', lastTranslateingTextLength.value)
      // 翻译剩余内容
      if (text.length - lastTranslateingTextLength.value < 10) {
        return
      }
      lastTranslateingTextLength.value = text.length
      const res = await translateSearch({
        text: text,
        source_lang: lang.code,
        target_lang: targetLang.code,
        translateApi: translateMode.value
      })
      console.log(res.data)
      currentLine.value = res.data
    }
    return
  }
  if (type === 'speechRecognizing') {
    translatedText.value = ""
    // 若超过展示阈值就进行翻译
    const currentLength = new TextEncoder().encode(text).length
    console.log('currentLength ', currentLength)
    console.log('lastRecognizingTextLength ', lastRecognizingTextLength.value)
    if (currentLength >= MIN_DISPLAY_BYTES && currentLength - lastRecognizingTextLength.value >= MIN_DISPLAY_BYTES) {
      lastRecognizingTextLength.value = currentLength
      // 生成唯一标识，防止多次请求错位
      const currentId = Date.now()
      currentRecognizingId.value = currentId
      currentRecognizingText.value = text

      try {
        const res = await translateSearch({
          text: text,
          source_lang: lang.code,
          target_lang: targetLang.code,
          translateApi: translateMode.value
        })

        // 只更新当前识别项
        if (currentRecognizingId.value === currentId) {
          currentLine.value = res.data
        }
      } catch (err) {
        console.error('speechRecognizing 翻译失败', err)
      }
    }
  }

  if (type === 'speechRecognized') {
    try {
      lastRecognizingTextLength.value = 0
      const res = await translateSearch({
        text: text,
        source_lang: lang.code,
        target_lang: targetLang.code,
        translateApi: translateMode.value
      })

      // 替换当前识别中的展示
      if (currentLine.value) {
        currentLine.value = ''
        currentRecognizingText.value = ''
        currentRecognizingId.value = null
      }
      translatedLines.value.push({text:speaker + res.data,hide:false})

      // 限制翻译行数（可选）
      if (translatedLines.value.length > 10) {
        translatedLines.value.shift()
      }
    } catch (err) {
      console.error('speechRecognized 翻译失败', err)
    }
  }
}

// 添加窗口拖动相关代码
const handleMouseDown = (e) => {
  // 添加拖动样式类
}

const handleMouseUp = () => {
  // 移除拖动样式类
}

const handleMouseEnter = () => {
  console.log('mouse entered')
  showButtons.value = true
}

const handleMouseLeave = () => {
  console.log('mouse left')
  showButtons.value = false
}
  </script>
  
<style lang="scss">
.mode-select{
  -webkit-app-region: no-drag !important;
  z-index: 9999 !important;
}
</style>
  <style scoped>
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  -webkit-app-region: drag;
}

.drag-css{
   -webkit-app-region: drag !important;
}

.container {
  /* padding: 10px; */
  font-size: 22px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  color: #fff;
  -webkit-app-region: drag;
  background-color: #000000;
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.line {
  padding: 0 10px;
  font-size: 22px;
  margin-bottom: 6px;
  /*-webkit-app-region: no-drag; 文本区域不可拖动，保持可选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.recognizing {
  color: gray;
  font-style: italic;
}

/* 底部按钮栏样式 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2px 0;
  display: flex;
  justify-content: flex-end;
  z-index: 100;
  align-items: center;
  background-color: transparent;
  -webkit-app-region: no-drag;
}

.bottom-bar button {
  padding: 5px 10px;
  cursor: pointer;
}
.right-icon{
  width: 60px;
  height: 32px;
  display: flex;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  background-color: red;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}
/* 包裹 icon 的正方形 div */
.icon-wrapper {
  width: 35px;
  height: 30px;
  display: flex;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  background-color: #aba9ab;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  -webkit-app-region: no-drag;
  margin-left: 3px;
}

.icon-wrapper:last-child {
  margin-right: 6px;
}

.icon-wrapper:hover {
  background-color: #888888;
  transform: scale(1.05);
}

.icon-wrapper:active {
  background-color: #666666;
  transform: scale(0.95);
}
.content{
  margin-top: 40px;
  height: calc(100vh - 40px);
  overflow-y: auto;
  -webkit-app-region: no-drag;
  z-index: 100;
}

/* Add scrollbar styling */
.content::-webkit-scrollbar {
  width: 18px;
  z-index: 2000;
  -webkit-app-region: no-drag;

}

.content::-webkit-scrollbar-track {
  background: transparent;
  -webkit-app-region: no-drag;
}

.content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  -webkit-app-region: no-drag;
}

.content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.top-css{
  background-color: #e6e6e6;
}
.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: #fff;
  -webkit-app-region: drag;
  z-index: 100;
  padding: 2px 10px;
  height: 40px;
}
.left-text {
  font-size: 20px;
  font-weight: bold;
  color: #aba9ab;
  -webkit-app-region: drag;
}
.right-controls {
  display: flex;
  align-items: center;
  -webkit-app-region: no-drag;
}

:deep(.el-select) {
  width: 120px;
  margin-right: 8px;
  -webkit-app-region: no-drag;
}

:deep(.el-input__wrapper) {
  background-color: #aba9ab;
  -webkit-app-region: no-drag;
}

:deep(.el-input__inner) {
  color: #000;
  -webkit-app-region: no-drag;
}

:deep(.el-select-dropdown) {
  -webkit-app-region: no-drag;
  z-index: 9999 !important;
}

:deep(.el-popper) {
  -webkit-app-region: no-drag;
  z-index: 9999 !important;
}
.scroll-to-bottom {
    position: fixed;
    right: 20px;
    bottom: 50px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #07c160;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    z-index: 9999;
    -webkit-app-region: no-drag;
}
</style>

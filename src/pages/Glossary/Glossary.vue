<template>
    <!--  词汇维护页面-->
    <el-dialog
        v-model="glossaryVisibleLocal"
        center
        fullscreen
        :show-close="false"
        :before-close="beforeClose"
        @dragenter="handleDragEnter"
    >
        <template #header="{ titleId, titleClass}">
            <el-row>
                <el-col :xs="2" :sm="2" :md="2" :lg="2" :xl="1">
                    <div class="back" @click="cancelGlossary"> &lt; Back</div>
                </el-col>
                <el-col :xs="22" :sm="22" :md="22" :lg="22" :xl="22">
                    <div :id="titleId" :class="titleClass">{{ `Vocabulary(${editGlossaryScene.name})` }}</div>
                </el-col>
            </el-row>
        </template>
        <div class="vocabulary" v-show="!uploading">
            <el-row>
                <div class="buttonBox">
                    <el-input v-model="searchValue" clearable @input="handelSearch" placeholder="Search Info" style="margin-right: 12px"></el-input>
                    <el-button type="default" @click="handelSearch">Search</el-button>
                    <el-button type="primary" @click="handleGlossaryAdd">Add</el-button>
                    <el-upload
                        :action='url'
                        class="upload"
                        accept=".xlsx, .xls"
                        :show-file-list="false"
                        :multiple="false"
                        :on-success="afterUpload"
                        :on-error="afterUpload"
                    >
                        <el-button type="primary">Upload</el-button>
                    </el-upload>
                    <el-button type="primary" @click="downloadGlossary">Download</el-button>
                </div>
            </el-row>
            <el-row>
                <el-table v-loading="loading" :data="glossaryData" border>
                    <el-table-column type="index" label="Index" width="100" align="center"/>
                    <el-table-column sortable prop="glossaryA" label="Primary" align="center"/>
                    <el-table-column prop="glossaryB" label="Secondary" align="center"/>
                    <el-table-column sortable prop="createAt" label="Create Date" align="center"/>
                    <el-table-column label="Operate" width="200" align="center">
                        <template #default="scope">
                            <el-button
                                @click="editGlossaryForm(scope.row)"
                                size="small"
                                type="default"
                            >Edit
                            </el-button>
                            <el-popconfirm
                                width="220px"
                                title="Are you sure to delete?"
                                @confirm="deleteGlossaryForm(scope.row)"
                            >
                                <template #reference>
                                    <el-button
                                        type="danger"
                                        size="small"
                                    >Delete
                                    </el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <div class="pagination">
                <el-pagination
                    layout="prev, pager, next"
                    :total="total"
                    :current-page="currentPage"
                    @current-change="handelChangeCurrent"
                />
            </div>
        </div>
    </el-dialog>

    <!--  词汇新增、编辑页面-->
    <el-dialog
        v-model="glossaryAddVisible"
        :title="glossaryAddForm.id !== undefined ? 'Edit Vocabulary' : 'Add Vocabulary'"
        center
        destroy-on-close
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        width="450"
        @keydown.enter="saveGlossaryForm(glossaryRef)"
    >
        <el-form ref="glossaryRef" :model="glossaryAddForm" :rules="addRules">
            <el-row justify="center">
                <el-col :span="20">
                    <el-form-item label="A" prop="glossaryA" label-width="40">
                        <el-input v-model="glossaryAddForm.glossaryA"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row justify="center">
                <el-col :span="20">
                    <el-form-item label="B" prop="glossaryB" label-width="40">
                        <el-input v-model="glossaryAddForm.glossaryB"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row justify="end">
                <el-col :span="7">
                    <el-button type="default" @click="cancelGlossaryForm">Cancel</el-button>
                </el-col>
                <el-col :span="4">
                    <el-button type="primary" @click="saveGlossaryForm(glossaryRef)">Save</el-button>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>

    <DragUpload
        v-model:uploading="uploading"
        currentScene="glossary"
        :scene-id="editGlossaryScene.id"
        :lang-list="langList"
        :afterUpload="handleAfterUpload"
    />
</template>

<script setup>
import {reactive, ref, watch} from "vue";
import {ElMessage} from "element-plus";
import {addGlossary, deleteGlossary, downGlossary, getGlossary} from "@/api/GlossaryApi";
import DragUpload from "@/pages/components/DragUpload.vue";

// 词典数据集
const glossaryData = ref([])
// 词典新增表单数据
const glossaryAddForm = ref({})
const glossaryVisibleLocal = ref(false)
const glossaryAddVisible = ref(false)
const glossaryRef = ref()
// 当前页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchValue = ref()
const addRules = reactive({
    glossaryA: [
        {required: true, message: 'Please Enter Target', trigger: 'blur'},
    ],
    glossaryB: [
        {required: true, message: 'Please Enter Content', trigger: 'blur'},
    ]
})
const url = ref('')
const Ashow = ref()
const Bshow = ref()
const uploading = ref(false) //是否展示拖拽上传
const loading = ref(false)

const props = defineProps({
    glossaryVisible: {type: Boolean},
    cancelGlossary: {type: Function},
    editGlossaryScene: {type: Object},
    langList: {type: Array},
    glossarySceneId: {type: Number},
    // 用于当词汇页新增、修改、删除、上传的时候，检测是否为已选择的场景摧毁
    // 如果是的话，需要实时更新glossaryRes，来去更新翻译数据集
    checkIsGlossaryScene: {type: Function}
})

watch(
    () => props.glossaryVisible,
    (newValue, oldValue) => {
        glossaryVisibleLocal.value = newValue
        if(process.env.NODE_ENV=='development'){
            url.value = `/conf/asr/glossary/import?userId=${localStorage.getItem('userData') ? JSON.parse(localStorage.getItem('userData')).userId : ''}&sceneId=${props.editGlossaryScene.id}`
        }else{
            url.value = `https://boothapi.gtmeeting.com/conf/asr/glossary/import?userId=${localStorage.getItem('userData') ? JSON.parse(localStorage.getItem('userData')).userId : ''}&sceneId=${props.editGlossaryScene.id}`
        }

        getGlossaryData()
    }
)
watch(
    () => props.langList,
    (newValue, oldValue) => {
        newValue.forEach(item => {
            if (item.main) {
                Ashow.value = `A(${item.show})`
            } else {
                Bshow.value = `B(${item.show})`
            }
        })
    }
)
// 搜索按钮的方法
const handelSearch = () => {
    currentPage.value = 1
    getGlossaryData()
}
// 分页获取词汇数据的接口
const getGlossaryData = () => {
    loading.value = true
    getGlossary({
        startPage: currentPage.value - 1,
        pageNum: pageSize.value,
        searchInfo: searchValue.value,
        sceneId: props.editGlossaryScene.id
    }).then(res => {
        glossaryData.value = res.data.data
        total.value = res.data.count
        loading.value = false
    })
}
// 切换页数
const handelChangeCurrent = (number) => {
    currentPage.value = number
    getGlossaryData()
}
// 上传后的方法
const afterUpload = res => {
    if (res.success) {
        ElMessage.success('Upload Success!');
        getGlossaryData()
        props.checkIsGlossaryScene(props.editGlossaryScene.id)
        return
    }
    ElMessage.error(res.message)
}
// 下载的方法
const downloadGlossary = () => {
    downGlossary({sceneId: props.editGlossaryScene.id})
}
// 打开词汇新增编辑页面
const handleGlossaryAdd = () => {
    glossaryAddVisible.value = true
}
// 词汇新增、编辑页面保存的方法
const saveGlossaryForm = (glossaryRef) => {
    console.log('save glossary ',props.editGlossaryScene)
    let id = props.editGlossaryScene.id
    glossaryRef.validate(valid => {
        if (valid) {
            addGlossary({...glossaryAddForm.value, sceneId: id}).then(res => {
                if (res.success) {
                    ElMessage.success('Add Success!')
                    cancelGlossaryForm()
                    getGlossaryData()
                    props.checkIsGlossaryScene(props.editGlossaryScene.id)
                } else {
                    ElMessage.error(res.msg)
                }
            })
        }
    })
}
// 新增、编辑页面取消的方法
const cancelGlossaryForm = () => {
    glossaryAddForm.value = {}
    glossaryAddVisible.value = false;
}
// 打开编辑页面的方法
const editGlossaryForm = (item) => {
    glossaryAddForm.value = {...item}
    glossaryAddVisible.value = true
}
// 删除的接口
const deleteGlossaryForm = (delItem) => {
    deleteGlossary({ids: [delItem.id]}).then(res => {
        if (res.success) {
            getGlossaryData()
            ElMessage.success('Delete Success!')
            props.checkIsGlossaryScene(props.editGlossaryScene.id)
        } else {
            ElMessage.error(res.msg)
        }
    })
}
// 结束前的回调，当点击ESC返回时，主动去修改visible的值
const beforeClose = done => {
    props.cancelGlossary()
    done()
}
// 拖拽文件进来窗口触发的操作
const handleDragEnter = event => {
    event.preventDefault();
    uploading.value = true
}
// 拖拽上传完成后的操作
const handleAfterUpload = () => {
    getGlossaryData()
    props.checkIsGlossaryScene(props.editGlossaryScene.id)
}
</script>

<style lang="scss" scoped>
.back {
    cursor: pointer;
    color: #07c160;
}

.buttonBox {
    display: flex;
    margin-bottom: 10px;

    .upload {
        margin: 0 12px;
    }
}

.pagination {
    margin-top: 10px;
    display: flex;
    justify-content: end;
}
</style>

<template>
    <!--  词汇场景-->
    <el-dialog
        v-model="glossarySceneVisibleLocal"
        center
        fullscreen
        :show-close="false"
        :before-close="beforeClose"
        destroy-on-close
        @dragenter="handleDragEnter"
    >
        <template #header="{ titleId, titleClass}">
            <el-row>
                <el-col :xs="2" :sm="2" :md="2" :lg="2" :xl="1">
                    <div class="back" @click="closeGlossaryScene"> &lt; Back</div>
                </el-col>
                <el-col :xs="22" :sm="22" :md="22" :lg="22" :xl="22">
                    <div :id="titleId" :class="titleClass">Vocabulary List</div>
                </el-col>
            </el-row>
        </template>
        <div class="glossaryScene">
            <el-row class="buttonBox" :gutter="10">
                <el-col :span="4">
                    <el-input v-model="searchValue" clearable @input="handelSearchGlossaryScene" placeholder="Search Info"></el-input>
                </el-col>
                <el-col :span="6">
                    <div>
                        <el-button type="default" @click="handelSearchGlossaryScene">Search</el-button>
                        <el-button type="primary" @click="handleGlossarySceneAdd">Add</el-button>
                    </div>
                </el-col>
                <el-col :span="14">
                    <div style="display: flex;">
                        <div style="width: 120px;display: flex;justify-content:center;align-items: center">Choose List:</div>
                        <el-select v-model="glossarySceneIdLocal" placeholder="Select" multiple
                                   @change="changeGlossaryScene">
                            <el-option
                                v-for="item in allGlossarySceneData"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </div>

                </el-col>
            </el-row>
            <el-row>
                <el-table v-loading="loading" :data="glossarySceneData" border>
                    <el-table-column type="index" label="Index" width="100" align="center"/>
                    <el-table-column prop="name" label="Name" align="center"/>
                    <el-table-column label="Operate" width="350" align="center">
                        <template #default="scope">
                            <el-button
                                @click="openGlossary(scope.row)"
                                size="small"
                                type="default"
                            >Management
                            </el-button>
                            <el-button
                                @click="editGlossarySceneForm(scope.row)"
                                size="small"
                                type="default"
                                v-if="!scope.row.sourceUserId"
                            >Edit
                            </el-button>
                            <el-button
                                @click="shareGlossarySceneForm(scope.row)"
                                size="small"
                                type="default"
                                v-if="!scope.row.sourceUserId"
                            >Share
                            </el-button>
                            <el-popconfirm width="220px" title="Are you sure to delete?" @confirm="deleteGlossarySceneForm(scope.row)">
                                <template #reference>
                                    <el-button
                                        type="danger"
                                        size="small"
                                        v-if="!scope.row.sourceUserId"
                                    >Delete
                                    </el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <div class="pagination">
                <el-pagination layout="prev, pager, next" :total="total" :current-page="currentPage"
                               @current-change="handelChangeCurrent"/>
            </div>
        </div>
    </el-dialog>
    <!-- 词汇表分享页面 -->
    <el-dialog v-model="glossarySceneShareVisible" title="Share" width="500">
        <el-form>
            <el-form-item
                v-for="(item, index) in glossarySceneShareUserList"
                :key="index"
                :label="`User ${index + 1}`"
                :label-width="formLabelWidth"
                >
                <el-input v-model="item.email" clearable placeholder="Please Input User Email" />
            </el-form-item>
        </el-form>
        <template #footer>
        <div class="dialog-footer">
            <el-button @click="glossarySceneShareVisible = false">Cancel</el-button>
            <el-button type="primary" @click="saveGlossarySceneShareList">
            Confirm
            </el-button>
        </div>
        </template>
  </el-dialog>
    <!--  词汇场景新增编辑页面-->
    <el-dialog
        v-model="glossarySceneAddVisible"
        :title="glossarySceneAddForm.id !== undefined ? 'Edit Vocabulary Scene' : 'Add Vocabulary Scene'"
        center
        destroy-on-close
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        width="350"
        @keydown.enter="saveGlossarySceneForm(glossarySceneRef)"
    >
        <el-form ref="glossarySceneRef" :model="glossarySceneAddForm" :rules="addRules">
            <el-row justify="center">
                <el-col :span="20">
                    <el-form-item label="Name" prop="name" label-width="30">
                        <el-input v-model="glossarySceneAddForm.name"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row justify="end">
                <el-col :span="7">
                    <el-button type="default" @click="cancelGlossarySceneForm">Cancel</el-button>
                </el-col>
                <el-col :span="4">
                    <el-button type="primary" @click="saveGlossarySceneForm(glossarySceneRef)">Save</el-button>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>

    <!-- 词汇页面 -->
    <Glossary
        :glossaryVisible="glossaryVisible"
        :cancelGlossary="cancelGlossary"
        :editGlossaryScene="editGlossaryScene"
        :langList="langList"
        :glossarySceneId="glossarySceneIdLocal"
        :checkIsGlossaryScene="checkIsGlossaryScene"
    />

    <!-- 拖拽上传 -->
    <DragUpload
        v-model:uploading="uploading"
        currentScene="glossary"
        :lang-list="langList"
        :all-glossary-scene-data="allGlossarySceneData"
        :afterAddScene="handleAfterAddScene"
        :after-upload="handleAfterAddScene"
    />
</template>

<script setup>
import {reactive, ref, watch} from "vue";
import {addGlossaryScene, deleteGlossaryScene, getAllGlossaryScene, getGlossaryScene, getGlossaryByIds,saveGlossarySceneShare} from "@/api/GlossaryApi";
import {ElMessage} from "element-plus";
import Glossary from "@/pages/Glossary/Glossary.vue";
import DragUpload from "@/pages/components/DragUpload.vue";

const glossarySceneRef = ref()
const glossarySceneData = ref([])
const allGlossarySceneData = ref([])
const glossarySceneAddForm = ref({})
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchValue = ref()
const glossarySceneAddVisible = ref(false)
const glossarySceneShareVisible = ref(false)
const nowChooseScene = ref(null)
const glossarySceneShareUserList = ref([{email:null},{email:null},{email:null},{email:null}])
const addRules = reactive({
    name: [
        {required: true, message: 'Please Enter Scene Name', trigger: 'blur'},
    ]
})
// 用户选择的当前翻译的词汇场景
const glossarySceneIdLocal = ref()
// 用户正在编辑的词汇场景条目
const editGlossaryScene = ref({})
// 是否展示词汇页面
const glossaryVisible = ref(false)
// 是否展示词汇场景页面
const glossarySceneVisibleLocal = ref(false)
// 表格数据加载
const loading = ref(false)
// 拖拽上传中反馈展示
const uploading = ref(false)
const props = defineProps({
    // 词汇场景页面是否展示
    glossarySceneVisible: {
        type: Boolean
    },
    // 关闭词汇场景页面的方法
    closeGlossaryScene: {
        type: Function
    },
    // 当前语言列表
    langList: {
        type: Array
    },
    // 用户选择的当前翻译的词汇场景
    glossarySceneId: {
        type: Number
    },
    // 改变用户选择词汇场景的方法
    changeGlossaryScene: {
        type: Function
    },
    // 用于当词汇页新增、修改、删除、上传的时候，检测是否为已选择的场景摧毁
    // 如果是的话，需要实时更新glossaryRes，来去更新翻译数据集
    checkIsGlossaryScene: {
        type: Function
    },
    clearGlossarySceneId: {
        type: Function
    }
})

watch(
    () => props.glossarySceneId,
    (newValue, oldValue) => {
        glossarySceneIdLocal.value = newValue
    }
)
watch(
    () => props.glossarySceneVisible,
    (nValue, oValue) => {
        glossarySceneVisibleLocal.value = nValue
        if (nValue) {
            // 每次点开dialog时，获取最新数据
            getGlossarySceneData()
            getAllGlossarySceneData()
        }
    }
)
// 获取表格分页数据
const getGlossarySceneData = () => {
    loading.value = true
    getGlossaryScene({
        startPage: currentPage.value - 1,
        pageNum: pageSize.value,
        searchInfo: searchValue.value
    }).then(res => {
        glossarySceneData.value = res.data.data
        total.value = res.data.count
        loading.value = false
    })
}
// 获取所有词汇场景，用来选择场景
const getAllGlossarySceneData = () => {
    getAllGlossaryScene({}).then(res => {
        allGlossarySceneData.value = [...res.data]
    })
}
// 搜索框的查询接口
const handelSearchGlossaryScene = () => {
    currentPage.value = 1
    getGlossarySceneData()
}
// 展示新增框
const handleGlossarySceneAdd = () => {
    glossarySceneAddVisible.value = true
}
// 取消新增框
const cancelGlossarySceneForm = () => {
    glossarySceneAddForm.value = {}
    glossarySceneAddVisible.value = false;
}
// 新增和编辑词汇场景条目的方法
const saveGlossarySceneForm = (glossarySceneRef) => {
    glossarySceneRef.validate(valid => {
        if (valid) {
            addGlossaryScene(glossarySceneAddForm.value).then(res => {
                if (res.success) {
                    ElMessage.success('Add Success!')
                    cancelGlossarySceneForm()
                    getGlossarySceneData()
                    getAllGlossarySceneData()
                } else {
                    ElMessage.error(res.msg)
                }
            })
        }
    })
}
// 打开编辑框的方法
const editGlossarySceneForm = (item) => {
    glossarySceneAddForm.value = {...item}
    glossarySceneAddVisible.value = true
}
// 打开分享词汇表弹框
const shareGlossarySceneForm = (item) =>{
    nowChooseScene.value = item
    let emailArr = []
    if(item.shareUserEmails){
        emailArr = item.shareUserEmails.split(',')
    }
    let emailAddressArr = []
    emailArr.forEach(element => {
        let info = {email: element}
        emailAddressArr.push(info)
    });
    while (emailAddressArr.length < 4) {
        emailAddressArr.push({ email: null });
    }
    glossarySceneShareUserList.value = emailAddressArr
    glossarySceneShareVisible.value = true
}
//保存词汇表共享用户列表
const saveGlossarySceneShareList = ()=>{
    let emailArr = []
    glossarySceneShareUserList.value.forEach(item=>{
        if(item.email){
            emailArr.push(item.email)
        }
    })
    let request = {
        id: nowChooseScene.value.id,
        emails: emailArr
    }
    saveGlossarySceneShare(request).then(res=>{
        console.log('save share res ',res)
        getGlossarySceneData()
        getAllGlossarySceneData()
        ElMessage.success('Success!')
        glossarySceneShareVisible.value = false
    }).catch(err=>{
        ElMessage.error(res.msg)
    })
}
// 删除词汇场景的方法
const deleteGlossarySceneForm = (delItem) => {
    deleteGlossaryScene(delItem.id).then(res => {
        if (res.success) {
            getGlossarySceneData()
            getAllGlossarySceneData()
            ElMessage.success('Delete Success!')
            if (glossarySceneIdLocal.value.includes(delItem.id)) {
                props.clearGlossarySceneId(delItem.id)
            }
        } else {
            ElMessage.error(res.msg)
        }
    })
}
// 切换页数
const handelChangeCurrent = (number) => {
    currentPage.value = number
    getGlossarySceneData()
}
// 打开词汇页面的方法
const openGlossary = (item) => {
    editGlossaryScene.value = item
    glossaryVisible.value = true
}
// 关闭词汇页面的方法
const cancelGlossary = () => {
    glossaryVisible.value = false
}
// 结束前的回调，当点击ESC返回时，主动去修改visible的值
const beforeClose = done => {
    props.closeGlossaryScene()
    done()
}
// 拖拽文件进来窗口触发的操作
const handleDragEnter = event => {
    event.preventDefault();
    uploading.value = true
}
// 导入新增场景名称后刷新数据
const handleAfterAddScene = () => {
    getGlossarySceneData()
    getAllGlossarySceneData()
}
</script>

<style lang="scss" scoped>
.back {
    cursor: pointer;
    color: #07c160;
}

.buttonBox {
    margin-bottom: 10px;
}

.pagination {
    margin-top: 10px;
    display: flex;
    justify-content: end;
}
</style>

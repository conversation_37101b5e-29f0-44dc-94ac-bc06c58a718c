<template>
    <div class="container">
        <div class="loginBox">
            <div class="formBox">
                <div class="logoBox">
                    <div class="title">Terp Mate</div>
                </div>
                <el-form :model="loginForm" :rules="loginRules" ref="loginRef" class="loginForm">
                    <el-form-item prop="emailAddress">
                        <el-input v-model="loginForm.emailAddress" type="text" placeholder="Email" :prefix-icon="Message"/>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input @keyup.enter="handleLogin(loginRef)" v-model="loginForm.password" type="password" placeholder="Password" :prefix-icon="Lock" show-password/>
                    </el-form-item>
                    <el-form-item>
                    <q-checkbox v-model="rememberPassword" size="30px">
                        Remember Password
                    </q-checkbox>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="handleLogin(loginRef)" type="primary" size="large">Login</el-button>
                    </el-form-item>
                    <el-form-item>
                        <div class="bottomButton">
                            <el-link :underline="false" @click="forgotPassword" type="info">Forgot Password?</el-link>
                            <el-link :underline="false" @click="signup">Sign Up</el-link>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="rightBox">
            <div class="rightBoxTop">
                <div class="topText">Interpreting Brilliance, Made Simple</div>
            </div>
            <div class="rightBoxBottom">
                <div class="bottomText">Powered by Green Terp AI</div>
            </div>
            <img class="image" src="@/assets/public/bg.png" alt="gt">
        </div>
        <el-dialog
            v-model="forceLandscape"
            :show-close="false"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            modal-class="landscape"
            center
            top="250px"
        >
            <template #header>
                <img src="@/assets/public/fanzhuan.png" alt="" style="width: 50px">
            </template>
            <div style="text-align: center">
                Please turn the screen to use~
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref} from "vue";
import {login} from "@/api/LoginApi";

import router from "@/router";
import {useRoute} from "vue-router";
import {ElMessage} from "element-plus";
import moment from "moment";
import {Message, Lock} from "@element-plus/icons-vue";
const {ipcRenderer} = require('electron')
const {proxy} = getCurrentInstance()
const route = useRoute()
const rememberPassword = ref(false)
const loginRef = ref()
const loginForm = ref({
    emailAddress: "",
    password: ""
})
const loginRules = reactive({
    emailAddress: [
        {required: true, message: 'Please Enter Email Address', trigger: 'blur'},
    ],
    password: [
        {required: true, message: 'Please Enter Password', trigger: 'blur'},
    ]
})
// 强制横屏遮罩层的开关
const forceLandscape = ref(false)

onMounted(async () => {
    console.log('env:',process.env.NODE_ENV)
    const first = route.query.first == undefined ? true:false;
    console.log('first: ',first,route)
    renderResize()
    window.addEventListener("onorientationchange" in window ? "orientationchange" : "resize", (e) => renderResize(e),false);
    let userEmail = ipcRenderer.sendSync('getUserEmail', 'tips')
    let password = await ipcRenderer.invoke("getPassword", null)
    if(password){
      loginForm.value.password = password
      rememberPassword.value = true
    }
    loginForm.value.emailAddress = userEmail
    if(first){
        handleLogin(loginRef.value)
    }
})
const handleLogin = async (loginRef) => {
    if (!loginRef) return
    await loginRef.validate(valid => {
        if (valid) {
            if(rememberPassword.value){
                ipcRenderer.send('savePassword',loginForm.value.password)
            }else{
                ipcRenderer.send('clearPassword',null)
            }
            // 登录处理
            login(loginForm.value).then(res => {
                if (res.success) {
                    // 存userId，并且设置过期时间为48小时
                    const userData = {
                        // overTime: moment().add(1, 'minutes'),
                        overTime: moment().add(48, 'hours'),
                        userId: res.data.userId
                    }
                    localStorage.setItem('userData', JSON.stringify(userData))
                    // localStorage.setItem('emailAddress', loginForm.value.emailAddress)
                    ipcRenderer.send('saveUserEmail',loginForm.value.emailAddress)
                    ElMessage.success('Login Success！')
                    router.push({path: route.query.redirect || '/interpretease'})
                } else {
                    ElMessage.error('Login Error！')
                }
            })
        }
    })
}
const forgotPassword = ()=> {
    window.open('https://admin.gtmeeting.com/#/findPwd')
}
const signup = ()=> {
    window.open('https://admin.gtmeeting.com/#/register')
}
// 手机端强制横屏
const renderResize = ()=> {
    if (window.orientation === 180 || window.orientation === 0) {
        forceLandscape.value = true;
    }
    if (window.orientation === 90 || window.orientation === -90 ){
        forceLandscape.value = false;
    }
}
</script>

<style scoped>
.container {
    width: 100vw;
    height: 100vh;
    position: relative;
    background: #fff;
    overflow: hidden;
}

.loginBox {
    width: 60vw;
    height: 100vh;
    position: absolute;
    left: 0;
}

.logoBox {
    width: 400px;
    height: 30px;
}

.formBox {
    width: 400px;
    height: 300px;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.title {
    line-height: 30px;
    font-size: 24px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: bolder;
    background-color: #07c160;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.loginForm {
    display: flex;
    flex-direction: column;
    width: 70%;
    height: 60%;
    margin: 10% auto;
}

:deep(.el-input) {
    --el-input-height: 45px;
    --el-input-bg-color: #f6f6f6;
}

:deep(.el-input__inner) {
    color: #121212;
    font-size: 16px;
}

button {
    width: 100%;
    height: 40px;
    font-weight: bolder;
}

.bottomButton {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.rightBoxTop {
    width: 40vw;
    height: 50vh;
    background: #07c160;
    position: absolute;
    right: 0;
    top: 0;
}
.topText {
    width: 40vw;
    position: absolute;
    bottom: 5px;
    text-align: center;
    font-size: 24px;
    font-weight: bolder;
    color: #ffffff;
    z-index: 2;
    margin-left: 20px;
}
.rightBoxBottom {
    width: 40vw;
    height: 50vh;
    position: absolute;
    bottom: 0;
    right: 0;
}
.bottomText {
    width: 40vw;
    position: absolute;
    top: 5px;
    color: #121212;
    text-align: center;
    font-size: 24px;
    font-weight: bolder;
    z-index: 2;
}

.image {
    height: 40%;
    position: fixed;
    top: 52%;
    left: 60%;
    transform: translate(-50%, -50%);
}
:deep(.landscape) {
    --el-overlay-color-lighter: #d8e1e7;

    .el-dialog {
        border-radius: 15px;

        .el-dialog__header {
            margin-right: 0;
        }
    }
}

@media (max-width: 600px) {
    .rightBox {
        display: none;
    }
    .loginBox {
        width: 100vw;
    }
}
@media (max-width: 1100px) {
    .topText {
        font-size: 16px;
    }
    .bottomText {
        font-size: 16px;
    }
}
@supports(height: 100dvh) {
    .container { height: 100dvh; }
    .loginBox { height: 100dvh; }
    .rightBoxTop { height: 50dvh; }
    .rightBoxBottom { height: 50dvh; }
}
</style>

// const win = require('win-audio');

// // manage speaker volume
// const speaker = win.speaker;

// // manage mic volume
// const microphone = win.mic;

// export function getWinMic () {
//   return microphone.get() || 0
// }
// export function setWinMic (val) {
//   microphone.set(val)
// }

// export function getWinOutput () {
//   return speaker.get() || 0
// }
// export function setWinOutput (val) {
//   speaker.set(val)
// }

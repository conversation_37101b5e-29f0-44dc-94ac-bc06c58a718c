// translationData.transcript  展示数据
// translationData.id          数据id
// translationData.original    原始结果数据
// translationData.replace     被替换的数据
// translationData.glossary    被词汇翻译的数据
// translationData.isGlossary  是否被词汇翻译
// translationData.isReplace   是否被替换
// targetArr                  要翻译还是替换的数组
// operate                    要进行的操作：'replace'||'glossary'
import store from "@/store";

const replaceEn = (input, replaceA, replaceB) => {
    const patternA2B = new RegExp(`\\b(${replaceA})\\b`, 'gi');
    return input.replace(patternA2B, `<b>${replaceB}</b>`);
};

const escapeRegExp = (string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

// 🛡️ 安全的词汇表替换（避免破坏 HTML）
const safeGlossaryReplace = (input, A, B, color) => {
    if (!input) return input;

    const escapedA = escapeRegExp(A);
    const escapedB = escapeRegExp(B);

    const patternA = new RegExp(`\\b${escapedA}\\b`, 'gi');
    const patternB = new RegExp(`\\b${escapedB}\\b`, 'gi');

    // 替换 HTML 结构外的纯文本
    return input.replace(/(<[^>]+>)|([^<]+)/g, (match, htmlTag, text) => {
        if (htmlTag) return htmlTag;
        if (!text) return '';

        let result = text;

        if (patternA.test(text)) {
            result = text.replace(patternA, (m) => {
                return `<span id="colorSpan" style="color: ${color};">${m}<span style="-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;">(${B})</span></span>`;
            });
        } else if (patternB.test(text)) {
            result = text.replace(patternB, (m) => {
                return `<span id="colorSpan" style="color: ${color};">${m}<span style="-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;">(${A})</span></span>`;
            });
        }

        return result;
    });
};

export const switchTranslation = (translationData, targetArr, operate, lang) => {
    switch (operate) {
        case 'replace': {
            for (let i = 0; i < targetArr.length; i++) {
                const A = targetArr[i].replaceA;
                const B = targetArr[i].replaceB;

                if (lang.code === 'zh-CN' || lang.code === 'zh-HK' || lang.code === 'zh-TW' || lang.code === 'wuu-CN' || lang.code === 'yue-CN') {
                    let replace = translationData.replace.replace(new RegExp(A, 'gi'), B);
                    if (replace !== translationData.replace) {
                        translationData.replace = replace;
                        translationData.isReplace = true;
                        translationData.transcript = translationData.transcript.replace(new RegExp(A, 'gi'), `<b>${B}</b>`);
                    }
                } else {
                    translationData.replace = replaceEn(translationData.replace, A, B);
                    translationData.isReplace = true;
                    translationData.transcript = replaceEn(translationData.transcript, A, B);
                }
            }
            return translationData;
        }

        case 'glossary': {
            for (let i = 0; i < targetArr.length; i++) {
                const A = targetArr[i].glossaryA;
                const B = targetArr[i].glossaryB;
                const color = store.getters.getColor;

                if (lang.code === 'zh-CN' || lang.code === 'zh-HK' || lang.code === 'zh-TW' || lang.code === 'wuu-CN' || lang.code === 'yue-CN') {
                    let glossary = translationData.glossary.replace(new RegExp(A, 'gi'), `${A}(${B})`);
                    if (glossary === translationData.glossary) {
                        translationData.glossary = translationData.glossary.replace(new RegExp(B, 'gi'), `${B}(${A})`);
                        translationData.isGlossary = true;
                        translationData.transcript = safeGlossaryReplace(translationData.transcript, B, A, color);
                    } else {
                        translationData.glossary = glossary;
                        translationData.isGlossary = true;
                        translationData.transcript = safeGlossaryReplace(translationData.transcript, A, B, color);
                    }
                } else {
                    translationData.glossary = safeGlossaryReplace(translationData.glossary, A, B, color);
                    translationData.transcript = safeGlossaryReplace(translationData.transcript, A, B, color);
                    translationData.isGlossary = true;
                }
            }
            return translationData;
        }

        default: {
            return translationData;
        }
    }
};

export const refreshTranslation = (replaceRes, glossaryRes, translationRes = store.getters.getTranslationRes.length ? JSON.parse(store.getters.getTranslationRes) : [], lang) => {
    translationRes.forEach(item => {
        item.result.forEach(res => {
            res.transcript = res.original;
            res.replace = res.original;
            res.glossary = res.original;
            res.isGlossary = false;
            res.isReplace = false;

            switchTranslation(res, replaceRes, 'replace', lang);
            switchTranslation(res, glossaryRes, 'glossary', lang);
        });
    });
};

// message.js
import { ElMessage } from 'element-plus';

class Message {
    static success(message) {
        ElMessage({
            message,
            type: 'success',
            showClose: true,
            duration: 3000, // 自定义消息显示时间
            offset: 70, // 自定义消息位置偏移量
        });
    }

    static warning(message) {
        ElMessage({
            message,
            type: 'warning',
            showClose: true,
            duration: 3000,
            offset: 70, // 自定义消息位置偏移量
        });
    }

    static info(message) {
        ElMessage({
            message,
            type: 'info',
            showClose: true,
            duration: 3000,
            offset: 70, // 自定义消息位置偏移量
        });
    }

    static error(message) {
        ElMessage({
            message,
            type: 'error',
            showClose: true,
            duration: 3000,
            offset: 70, // 自定义消息位置偏移量
        });
    }
}

export default Message;

import CryptoJS from 'crypto-js';
let baseURL;
if(process.env.NODE_ENV=='development'){
  baseURL = "ws://117.72.99.27:8005"
} else {
  baseURL = 'wss://boothapi.gtmeeting.com/ws/'
}
class BoothWebsocketClient {
    constructor() {
        this.ws = null;
        this.url = '';
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000; // 3秒重连间隔
        this.heartbeatInterval = null;
        this.eventListeners = new Map();
    }

    // 连接方法
    connect() {
        this.url = baseURL;
        console.log('booth asr : ',this.url)
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(this.url);
                
                // 连接成功
                this.ws.onopen = () => {
                    console.log('WebSocket connected');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.emit('connected', { status: 'connected' });
                    resolve(true);
                };

                // 接收消息
                this.ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.emit('message', data);
                        
                        // 根据消息类型触发不同事件
                        if (data.type) {
                            this.emit(data.type, data);
                        }
                    } catch (error) {
                        console.error('Failed to parse message:', error);
                        this.emit('error', { error: 'Message parse error' });
                    }
                };

                // 连接关闭
                this.ws.onclose = () => {
                    console.log('WebSocket closed');
                    this.isConnected = false;
                    this.stopHeartbeat();
                    this.emit('disconnected', { status: 'disconnected' });
                    this.tryReconnect();
                };

                // 错误处理
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.emit('error', { error: 'Connection error' });
                    reject(error);
                };

            } catch (error) {
                console.error('Failed to create WebSocket:', error);
                reject(error);
            }
        });
    }

    // 重连机制
    tryReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            this.emit('reconnect_failed', { attempts: this.reconnectAttempts });
            return;
        }

        this.reconnectAttempts++;
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.emit('reconnecting', { attempt: this.reconnectAttempts });

        setTimeout(() => {
            this.connect(this.url).catch(() => {
                this.tryReconnect();
            });
        }, this.reconnectInterval);
    }

    // 心跳机制
    startHeartbeat(userId) {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({
                    source:"terpmeta",
                    type:"heartbeat",
                    timestamp:Date.now(),
                    userId
                 });
            }
        }, 10000); 
    }
    sendInit(userId){
        if (this.isConnected) {
            this.send({
                source:"terpmeta",
                type:"init",
                timestamp:Date.now(),
                userId
             });
        }
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    // 发送消息
    send(data) {
        if (!this.isConnected) {
            throw new Error('WebSocket is not connected');
        }
        
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        this.ws.send(message);
    }

    sendBinary(data) {
        if (!this.isConnected) {
            throw new Error('WebSocket is not connected');
        }
        
        this.ws.send(data);
    }

    // 事件监听器管理
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    off(event, callback) {
        if (!this.eventListeners.has(event)) return;
        
        const callbacks = this.eventListeners.get(event);
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
            callbacks.splice(index, 1);
        }
    }

    emit(event, data) {
        if (!this.eventListeners.has(event)) return;
        
        this.eventListeners.get(event).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`Error in event listener for ${event}:`, error);
            }
        });
    }

    // 关闭连接
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.stopHeartbeat();
            this.isConnected = false;
            this.eventListeners.clear();
        }
    }
}

// 创建单例实例
const boothWebsocketClient = new BoothWebsocketClient();

export default boothWebsocketClient;

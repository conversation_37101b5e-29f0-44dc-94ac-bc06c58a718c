// 语言代码映射表，将完整的语言代码转换为简化的语言代码
const LANGUAGE_CODE_MAP = {
  // 英语
  'en-US': 'en',
  'en-GB': 'en', 
  'en-AU': 'en',
  'en-CA': 'en',
  'en-AE': 'en',
  'en-ID': 'en',
  'en-IE': 'en',
  'en-IN': 'en',
  'en-NZ': 'en',
  
  // 中文
  'zh-CN': 'zh',
  'zh-HK': 'zh',
  'zh-TW': 'zh',
  'wuu-CN': 'zh',
  'yue-CN': 'zh',
  
  // 西班牙语
  'es-ES': 'es',
  'es-MX': 'es',
  'es-AR': 'es',
  
  // 法语
  'fr-FR': 'fr',
  'fr-CA': 'fr',
  
  // 德语
  'de-DE': 'de',
  'de-AT': 'de',
  'de-CH': 'de',
  
  // 意大利语
  'it-IT': 'it',
  'it-CH': 'it',
  
  // 葡萄牙语
  'pt-PT': 'pt',
  'pt-BR': 'pt',
  
  // 阿拉伯语
  'ar-SA': 'ar',
  
  // 日语
  'ja-JP': 'ja',
  
  // 韩语
  'ko-KR': 'ko',
  
  // 俄语
  'ru-RU': 'ru',
  
  // 其他语言
  'ca-ES': 'ca',
  'cs-CZ': 'cs',
  'da-DK': 'da',
  'el-GR': 'el',
  'hi-IN': 'hi',
  'hi-Latn': 'hi',
  'hr-HR': 'hr',
  'hu-HU': 'hu',
  'id-ID': 'id',
  'ms-MY': 'ms',
  'nb-NO': 'nb',
  'nl-NL': 'nl',
  'nl-BE': 'nl',
  'pl-PL': 'pl',
  'ro-RO': 'ro',
  'sk-SK': 'sk',
  'sv-SE': 'sv',
  'th-TH': 'th',
  'tr-TR': 'tr',
  'uk-UA': 'uk',
  'vi-VN': 'vi'
}

/**
 * 将语言列表转换为简化的语言代码数组
 * @param {Array} langList - 语言列表数组
 * @returns {Array} - 语言代码数组，如 ['en', 'zh']
 */
export const convertLanguageCodes = (langList) => {
  if (!Array.isArray(langList)) {
    return []
  }
  
  const convertedCodes = langList
    .filter(lang => lang.code && lang.code !== 'Auto') // 过滤掉Auto和无效的code
    .map(lang => {
      const code = lang.code
      // 如果在映射表中找到，返回映射的简化代码
      if (LANGUAGE_CODE_MAP[code]) {
        return LANGUAGE_CODE_MAP[code]
      }
      // 如果没找到，尝试提取语言代码的前缀部分
      const prefix = code.split('-')[0].toLowerCase()
      return prefix
    })
    .filter((code, index, array) => array.indexOf(code) === index) // 去重
  
  return convertedCodes
}

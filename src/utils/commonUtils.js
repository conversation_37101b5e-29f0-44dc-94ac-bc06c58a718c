// 如果超过10个标点，取前5个标点前的内容（包
export const getTextBeforeFifthPunctuation = (text) => {
  // 匹配常见中英文标点符号
  const punctuationRegex = /[.,!?，。！？]/g;
  let match;
  let punctuationIndexes = [];

  // 收集所有标点位置
  while ((match = punctuationRegex.exec(text)) !== null) {
    punctuationIndexes.push(match.index);
  }

  // 如果超过10个标点，取前5个标点前的内容（包含第5个标点）
  if (punctuationIndexes.length > 10) {
    const fifthIndex = punctuationIndexes[6]; // 第5个标点的 index
    return text.slice(0, fifthIndex + 1);
  } else {
    return null;
  }
}

export const mapSpeaker = (speakerId) =>{
  if (!speakerId || speakerId === "Unknown") {
    return "";
  }

  const match = speakerId.match(/^Guest-(\d+)$/);
  if (match) {
    const index = parseInt(match[1], 10) - 1; // 1 → 0
    return String.fromCharCode("A".charCodeAt(0) + index) + ": ";
  }

  // 非 Guest-x 情况（可选处理）
  return speakerId; // 或者 return "?"
}


//查看newText是否包含result，如果包含，则返回newText中result之后的内容
export const getTextAfterResult = (newText, result) => {
  const index = newText.indexOf(result);
  if (index !== -1) {
    return newText.slice(index + result.length);
  } else {
    return ''; // 如果未找到 result，返回空字符串
  }
}

// 检测中英文句号、问号、感叹号并进行强制断句
export const checkForForcedSentenceBreak = (text) => {
  // 匹配中英文句号、问号、感叹号
  const sentenceEndRegex = /[.!?。！？]/;
  const match = text.match(sentenceEndRegex);

  if (match) {
    const breakIndex = match.index + 1; // 包含标点符号
    const beforeBreak = text.slice(0, breakIndex);
    const afterBreak = text.slice(breakIndex);

    return {
      hasBreak: true,
      beforeBreak: beforeBreak.trim(),
      afterBreak: afterBreak.trim()
    };
  }

  return {
    hasBreak: false,
    beforeBreak: '',
    afterBreak: text
  };
}

/*
* 将图片的 url 转为 base64
* @param url 图片的 url
* @return base64
* */
export const urlToBase64 = async (url) => {
    const res = await fetch(url);
    const blob = await res.blob();
    const newBlob = new Blob([blob], { type: 'image/png' });

    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        // 处理读取成功的情况
        reader.onloadend = () => {
            resolve(reader.result);  // 返回 Base64 编码
        };
        // 将 Blob 转换为 Base64
        reader.readAsDataURL(newBlob);  // 异步转换 Blob 为 Base64
    });
}

/*
* 统一两个 p 标签高度的方法
* @param nodeA - 节点A
* @param nodeB - 节点B
* @return void
* */
export const changeHeight = (nodeA, nodeB) => {
    // 比较两个p标签高度，取较大值
    const heightA = nodeA.offsetHeight
    const heightB = nodeB.offsetHeight
    const maxHeight = Math.max(heightA, heightB)
    // 设置两个p标签高度一致
    nodeA.style.height = maxHeight + 'px'
    nodeB.style.height = maxHeight + 'px'
}

/*
* 处理源文件和翻译文件的高度方法
* @param source - 源文件ref
* @param target - 翻译文件ref
* @return void
* */
export const handleChangeHeight = (source, target) => {
    // 获取源文件的p标签
    const sourceP = source.querySelectorAll('p')
    const targetP = target.querySelectorAll('p')
    for (let i = 0; i < sourceP.length; i++) {
        // 获取两个p标签
        const nodeA = sourceP[i]
        const nodeB = targetP[i]
        // 调用统一高度的方法
        changeHeight(nodeA, nodeB)
    }
}


export default class DetectFun {
  constructor() {
    this.init();
  }

  init() {
    this.mystatus = {};
    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    this.mediaStreamSource = null;
    this.scriptProcessor = null;
    this.testStream = null;
  }

  beginDetect(callback, media) {
    if (!media) return;
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia(media).then((stream) => {
        this.testStream = stream;
        this.mediaStreamSource = this.audioContext.createMediaStreamSource(stream);
        this.scriptProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);
        this.mediaStreamSource.connect(this.scriptProcessor);
        this.scriptProcessor.connect(this.audioContext.destination);

        this.scriptProcessor.onaudioprocess = (e) => {
          let buffer = e.inputBuffer.getChannelData(0);
          let maxVal = Math.max.apply(Math, buffer);
          this.mystatus = {
            code: 200,
            num: Math.round(maxVal * 100)
          };
          callback(this.mystatus);
        };
      }).catch((error) => {
        this.mystatus = {
          code: 400,
          err: '获取音频时好像出了点问题。' + error
        };
        callback(this.mystatus);
      });
    } else {
      this.mystatus = {
        code: 500,
        err: '不支持获取媒体接口'
      };
      callback(this.mystatus);
    }
  }

  closeDetect() {
    return new Promise((resolve, reject) => {
      try {
        if (this.mediaStreamSource) {
          this.mediaStreamSource.disconnect();
          this.mediaStreamSource = null;
        }

        if (this.scriptProcessor) {
          this.scriptProcessor.disconnect();
          this.scriptProcessor = null;
        }

        if (this.testStream) {
          this.testStream.getTracks().forEach((track) => {
            track.stop();
          });
          this.testStream = null;
        }

        if (this.audioContext) {
          this.audioContext.close().then(() => {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            console.log('@1234 close mic detect');
            resolve();
          }).catch((error) => {
            reject(error);
          });
        } else {
          console.log('@1234 close mic detect');
          resolve();
        }
      } catch (error) {
        reject(error);
      }
    });
  }
}

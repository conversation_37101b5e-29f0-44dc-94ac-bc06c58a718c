var applescript = require("applescript");
var sudo = require('sudo-prompt');
const execSh = require("exec-sh");

export function resetMacSystemAudio() {
  var options = {
    name: 'GTBooth',
  };
  //需要执行两次
  let appName = 'Green Terp Audio'
  // let killorder = 'killall ' + appName
  let killorder = "ps -e | grep -v grep | grep 'Green Terp Audio.app' | awk '{print $1}' | xargs kill"
  // sudo.exec('launchctl kickstart -kp system/com.apple.audio.coreaudiod', options,
  //let order = "killall 'Green Terp Audio' && kill `ps -ax | grep 'coreaudiod' | grep 'sbin' |awk '{print $1}'`"
  let oorder = killorder + " && kill `ps -ax | grep 'coreaudiod' | grep 'sbin' |awk '{print $1}'`"
  sudo.exec(oorder, options,
    function(error, stdout, stderr) {
      if (error) {
        console.log(error)
      }
      if(stdout){

      }
      let order = 'open -a \"' + appName + '.app\"'
       setTimeout(()=>{
        console.log('open gta order exec')
          execSh(order,null,(err, stdout, stderr) => {
                console.log("open error: ", err);
                console.log("open stdout: ", stdout);
                console.log("open stderr: ", stderr);
          });
          sudo.exec(oorder, options,
            function(error, stdout, stderr) {
              if (error) {
                console.log(error)
              }
              if(stdout){

              }
              setTimeout(()=>{
                let order = 'open -a \"' + appName + '.app\"'
                console.log('open gta order exec')
                  execSh(order,null,(err, stdout, stderr) => {
                        console.log("open error: ", err);
                        console.log("open stdout: ", stdout);
                        console.log("open stderr: ", stderr);
                      });
              },2000);
              console.log('11111stdout: ' + stdout);
            }
          );
      },2000);
    }
  );

}

export function quitGtAudio(volume) {
    if (process.platform == 'darwin') {
      let script = `
      tell application "Green Terp Audio"
	    quit
      end tell
      `
      console.log(script)
      applescript.execString(script, function (err, res) {
        if (res) {
          console.log(res);
        }
      });
    } 
}

export function getGTAVersion(){
  let script = `
      -- 替换为你想要查询版本号的应用程序名称
      set appName to "Green Terp Audio"
      
      -- 获取应用程序的路径
      set appPath to POSIX path of (path to application appName)
      
      -- 构造读取 CFBundleShortVersionString 的命令
      set plistPath to appPath & "Contents/Info.plist"
      set getVersionCommand to "defaults read " & quoted form of plistPath & " CFBundleShortVersionString"
      
      -- 执行命令并获取版本号
      try
        set appVersion to do shell script getVersionCommand
        return appVersion
      on error errMsg
        return "Error: " & errMsg
      end try
    
    `
  return new Promise(function(resolve, reject) {
    applescript.execString(script, function(err, result) {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
}
  
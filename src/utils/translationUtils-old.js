// translationData.transcript  展示数据
// translationData.id          数据id
// translationData.original    原始结果数据
// translationData.replace     被替换的数据
// translationData.glossary    被词汇翻译的数据
// translationData.isGlossary  是否被词汇翻译
// translationData.isReplace   是否被替换
// targetArr                  要翻译还是替换的数组
// operate                    要进行的操作：'replace'||'glossary'
import store from "@/store";

const replaceEn = (input, replaceA, replaceB) => {
    const patternA2B = new RegExp(`\\b(${replaceA})\\b`, 'gi');
    let replaceValue = input.replace(patternA2B,  `<b>${replaceB}</b>`)
    // if(replaceValue === input){
    //     const patternB2A = new RegExp(`\\b(${replaceB})\\b`, 'gi');
    //     replaceValue = input.replace(patternB2A,  `<b>${replaceA}</b>`)
    // }
    return replaceValue
}
const escapeRegExp = (string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  };
  
const glossaryEn = (input, glossaryA, glossaryB) => {
    const escapedA = escapeRegExp(glossaryA);
    const escapedB = escapeRegExp(glossaryB);
    if(!input)return
    const patternA2B = new RegExp(`\\b(${escapedA})\\b`, 'gi');
    let glossaryValue = input.replace(patternA2B, `<span id="colorSpan" style="color: ${store.getters.getColor};">${glossaryA}<span style="-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;">(${glossaryB})</span></span>`);
    
    if (glossaryValue === input) {
        const patternB2A = new RegExp(`\\b(${escapedB})\\b`, 'gi');
        glossaryValue = input.replace(patternB2A, `<span id="colorSpan" style="color: ${store.getters.getColor};">${glossaryB}<span style="-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;">(${glossaryA})</span></span>`);
    }

    return glossaryValue;
}


export const switchTranslation = (translationData, targetArr, operate, lang) => {
    switch (operate) {
        case 'replace': {
            for (let i = 0; i < targetArr.length; i++) {
                const A = targetArr[i].replaceA
                const B = targetArr[i].replaceB
                // 根据当前asr语言 进行不同处理，中文replaceAll
                if (lang.code === 'zh-CN' || lang.code === 'zh-HK' || lang.code === 'zh-TW' || lang.code === 'wuu-CN' || lang.code === 'yue-CN'){
                    let replace = translationData.replace.replace(new RegExp(A, 'gi'), B)
                    if (replace !== translationData.replace) {
                        translationData.replace = replace
                        translationData.isReplace = true
                        translationData.transcript = translationData.transcript.replace(new RegExp(A, 'gi'), `<b>${B}</b>`)
                    }
                } else {
                    translationData.replace = replaceEn(translationData.replace,A,B)
                    translationData.isReplace = true
                    translationData.transcript = replaceEn(translationData.transcript,A,B)
                }
            }
            return translationData
        }

        case 'glossary': {
            for (let i = 0; i < targetArr.length; i++) {
                // 翻译是在替换后文本上进行替换
                const A = targetArr[i].glossaryA
                const B = targetArr[i].glossaryB
                if (lang.code === 'zh-CN' || lang.code === 'zh-HK' || lang.code === 'zh-TW' || lang.code === 'wuu-CN' || lang.code === 'yue-CN'){
                    let glossary = translationData.glossary.replace(new RegExp(A, 'gi'), `${A}(${B})`)
                    if (glossary === translationData.glossary){
                        translationData.glossary = translationData.glossary.replace(new RegExp(B, 'gi'), `${B}(${A})`)
                        translationData.isGlossary = true
                        translationData.transcript = translationData.transcript.replace(new RegExp(B, 'gi'), `<span id="colorSpan" style="color: ${store.getters.getColor};">${B}<span style="-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;">(${A})</span></span>`)
                    } else {
                        translationData.glossary = glossary
                        translationData.isGlossary = true
                        translationData.transcript = translationData.transcript.replace(new RegExp(A, 'gi'), `<span id="colorSpan" style="color: ${store.getters.getColor};">${A}<span style="-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;">(${B})</span></span>`)
                    }
                } else {
                    if(A == 'Span' || B == 'Span' || A == 'span' || B == 'span') {
                        translationData.isGlossary = true
                        return
                    }
                    translationData.glossary = glossaryEn(translationData.glossary,A,B)
                    translationData.isGlossary = true
                    translationData.transcript = glossaryEn(translationData.transcript,A,B)
                }
            }
            return translationData
        }
        default: { }
    }
}

export const refreshTranslation = (replaceRes, glossaryRes, translationRes = store.getters.getTranslationRes.length ? JSON.parse(store.getters.getTranslationRes) : [], lang) => {
    translationRes.forEach(item => {
        item.result.forEach(res => {
            res.transcript = res.original
            res.replace = res.original
            res.glossary = res.original
            res.isGlossary = false
            res.isReplace = false
            // 全量替换操作
            switchTranslation(res, replaceRes, 'replace', lang)
            // 全量翻译操作
            switchTranslation(res, glossaryRes, 'glossary', lang)
        })
    })
}

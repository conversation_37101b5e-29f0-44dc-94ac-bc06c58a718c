/**
 * 防抖
 * @returns {(function(): void)|*}
 * @param fn
 * @param wait
 * @param immediate
 */
export function debounce(fn, wait, immediate = false) {
    var timeout, result;
    return function () {
        var context = this
        var args = arguments
        clearTimeout(timeout)
        if (immediate) {
            var callNow = !timeout
            timeout = setTimeout(() => {
                timeout = null
            }, wait)
            if (callNow) result = fn.apply(context, args)
        } else {
            timeout = setTimeout(() => {
                fn.apply(context, args)
            }, wait)
        }
        return result
    }
}
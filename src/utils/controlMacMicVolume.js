//控制mac 麦克风音量
var applescript = require("applescript");

var scripts = {
  state: "input volume of (get volume settings) & output volume of (get volume settings) & output muted of (get volume settings)",
  volumeState: "output volume of (get volume settings)",
  inputState: "input volume of (get volume settings)",
  outputState: "output volume of (get volume settings)",
  muteState: "output muted of (get volume settings)",
  setOutput: "set volume output volume %s --100%",
  setInput: "set volume input volume %s --100%",
  increase: "set volume output volume (output volume of (get volume settings) + 10) --100%",
  decrease: "set volume output volume (output volume of (get volume settings) - 10) --100%",
  mute: "set volume with output muted",
  unmute: "set volume without output muted"
};

exports.setInputValue = function (value) {
  return new Promise(function(resolve, reject) {
    applescript.execString(scripts.setInput.replace("%s", value), function(err, result) {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
}

exports.getInputValue = function () {
  return new Promise(function(resolve, reject) {
    applescript.execString(scripts.inputState, function(err, result) {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
}

exports.setOutputValue = function (value) {
  return new Promise(function(resolve, reject) {
    applescript.execString(scripts.setOutput.replace("%s", value), function(err, result) {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
}

exports.getOutputValue = function () {
  return new Promise(function(resolve, reject) {
    applescript.execString(scripts.outputState, function(err, result) {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
}
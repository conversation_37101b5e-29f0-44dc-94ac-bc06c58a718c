import * as XLSX from "xlsx"
import FileSaver from "file-saver";

export function readExcelToJson(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            let data = new Uint8Array(e.target.result);
            let workbook = XLSX.read(data, { type: "array" });
            //将Excel 第一个sheet内容转为json格式
            let worksheet = workbook.Sheets[workbook.SheetNames[0]];
            let json = XLSX.utils.sheet_to_json(worksheet,{ header: 1 });
            resolve(json);
        };
        reader.readAsArrayBuffer(file);
    });
}
export function saveJsonToExcel(data, filename) {
    let sheet = XLSX.utils.json_to_sheet(data);
    let workbook = {
        SheetNames: ["sheet1"],
        Sheets: {
            sheet1: sheet,
        },
    };
    let wbout = XLSX.write(workbook, {
        bookType: "xlsx",
        bookSST: true,
        type: "array",
    });
    FileSaver.saveAs(
        new Blob([wbout], { type: "application/octet-stream" }),
        filename
    );
}


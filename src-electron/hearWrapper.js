/**
 * macos asr 管理
 */
import { app } from 'electron'
import path from 'path'
import {getTextAfterResult,getTextBeforeFifthPunctuation} from "@/utils/commonUtils";
const spawn = require('child_process').spawn

export default class HearWrapper {
  constructor(mainWindow) {
    this.mainWindow = mainWindow
    //mac在线模式持续的时长
    this.macOnlineModeInterval = 0
    this.timeInterval = null
    //新的进程是接管，还是不是接管
    this.newProcessIsTakeOver = false
    this.SAMPLE_RATE = 16000;
    this.hearProcess = null
    this.takeOverHearProcess = null
    this.lastAsrResult = null
    this.macModeReq = null
    this.translatedText = ''
  }

  startTime() {
    this.macOnlineModeInterval = 0
    this.timeInterval = setInterval(() => {
      this.macOnlineModeInterval++
      console.log(this.macOnlineModeInterval)
      //8分钟后开启新的进程，进行接管
      if (8 * 60 == this.macOnlineModeInterval) {
        if (this.hearProcess) {
          console.log('## start takeover online process')
          this.startTakeOverOnlineHearProcess(this.macModeReq)
          this.newProcessIsTakeOver = true
        } else if (this.takeOverHearProcess) {
          this.startOnlineHearProcess(this.macModeReq)
          this.newProcessIsTakeOver = false
          console.log('## start online process')
        }
      }
    }, 1000);
  }

  restartTime() {
    this.stopTime()
    this.startTime()
  }

  stopTime() {
    clearInterval(this.timeInterval)
  }

  startOnlineHearProcess(req) {
    let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gtasr') : path.join(app.getAppPath(), '../../lib/gtasr');
    let args = ['-l', req.language, '-p', '-w', req.device];
    console.log('Executing command:', pkgpPath, args.join(' '));
    this.hearProcess = spawn(pkgpPath, args);
    //开始计时
    this.restartTime()
    this.hearProcess.stdout.on('data', (data) => {
      //停掉接管进程
      if (!this.newProcessIsTakeOver && this.takeOverHearProcess) {
        this.takeOverHearProcess.kill('SIGTERM');
        this.takeOverHearProcess = null;
        console.log('kill hearprocess ', this.takeOverHearProcess)
      }
      let output = data.toString()
      const matches = [...output.matchAll(/({[\s\S]*?})/g)];
      let lastJsonStr = output
      if (matches.length > 0) {
        lastJsonStr = matches[matches.length - 1][1];
      }
      let asrInfo =  JSON.parse(lastJsonStr)
      let text = asrInfo.result
      // 讯飞按照策略断句结果
      if (this.translatedText && this.translatedText.length > 0) {
        let newText = getTextAfterResult(text, this.translatedText)
        text = newText
      }
      console.log('after ', text.length)
      let textBeforeFifthPunctuation = getTextBeforeFifthPunctuation(text)
      let type = 'speechRecognized'
      if(textBeforeFifthPunctuation){
        this.translatedText += textBeforeFifthPunctuation
        type = 'speechRecognized'
        text = textBeforeFifthPunctuation
      }else{
        type = 'speechRecognizing'
      }
      asrInfo.result = text
      asrInfo.type = type
      let _info = { type, data: JSON.stringify(asrInfo) }
      if(this.mainWindow && this.mainWindow.webContents){
        this.mainWindow.webContents.send('asrResult', _info);
      }
      // if (this.mainWindow && this.mainWindow.webContents) {
      //   this.mainWindow.webContents.send('asrResult', { type: 'speechRecognized', data: output });
      // }
    });

    this.hearProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error('stderr:', output);
    });

    this.hearProcess.on('close', (code) => {
      console.log(`online gtasr 子进程退出，退出码 ${code}`);
    });
  }

  startTakeOverOnlineHearProcess(req) {
    let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gtasr') : path.join(app.getAppPath(), '../../lib/gtasr');
    let args = ['-l', req.language, '-p', '-w', req.device];
    // let args = ['-l', 'zh-CN', '-p', '-w', 'MacBook Pro麦克风'];
    console.log('Executing command:', pkgpPath, args.join(' '));
    this.takeOverHearProcess = spawn(pkgpPath, args);
    //开始计时
    this.restartTime()
    this.takeOverHearProcess.stdout.on('data', (data) => {
      //停掉识别进程
      if(this.newProcessIsTakeOver && this.hearProcess){
        this.hearProcess.kill('SIGTERM');
        this.hearProcess = null;
        console.log('kill hearprocess ',this.hearProcess)
      }
      let output = data.toString()
      const matches = [...output.matchAll(/({[\s\S]*?})/g)];
      let lastJsonStr = output
      if (matches.length > 0) {
        lastJsonStr = matches[matches.length - 1][1];
      }
      let asrInfo =  JSON.parse(lastJsonStr)
      let text = asrInfo.result
      // 讯飞按照策略断句结果
      if (this.translatedText && this.translatedText.length > 0) {
        let newText = getTextAfterResult(text, this.translatedText)
        text = newText
      }
      console.log('after ', text.length)
      let textBeforeFifthPunctuation = getTextBeforeFifthPunctuation(text)
      let type = 'speechRecognized'
      if(textBeforeFifthPunctuation){
        this.translatedText += textBeforeFifthPunctuation
        type = 'speechRecognized'
        text = textBeforeFifthPunctuation
      }else{
        type = 'speechRecognizing'
      }
      asrInfo.result = text
      asrInfo.type = type
      let _info = { type, data: JSON.stringify(asrInfo) }
      if(this.mainWindow && this.mainWindow.webContents){
        this.mainWindow.webContents.send('asrResult', _info);
      }
    });
  
    this.takeOverHearProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error('stderr:', output);
    });
  
    this.takeOverHearProcess.on('close', (code) => {
      console.log(`take over gtasr 子进程退出，退出码 ${code}`);
    });
  }

  startOfflineHearProcess(req) {
    let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gtasr') : path.join(app.getAppPath(), '../../lib/gtasr');
    let args = ['-l', req.language, '-p', '-w', req.device];
    // let args = ['-l', 'zh-CN', '-p', '-w', 'MacBook Pro麦克风'];
    console.log('Executing command:', pkgpPath, args.join(' '));
    this.hearProcess = spawn(pkgpPath, args);
    this.hearProcess.stdout.on('data', (data) => {
      const output = JSON.parse(data.toString());
      //判断当前的和上次的长度差，如果超过6个就强制断句，发送上一个的speechRecognized
      if(this.lastAsrResult){
        let segmentsLength = output.segmentsLength
        if(output.type != "speechRecognized" && this.lastAsrResult.type != "speechRecognized"  && this.lastAsrResult.segmentsLength - segmentsLength > 6){
          this.lastAsrResult.type = 'speechRecognized'
          this.mainWindow.webContents.send('asrResult', { type: 'speechRecognized', data: JSON.stringify(this.lastAsrResult) });
          this.lastAsrResult = null
        }
      }
      this.lastAsrResult = output
      console.log('stdout:', output);
      this.mainWindow.webContents.send('asrResult', { type: 'speechRecognized', data: JSON.stringify(output) });
    });
  
    this.hearProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error('stderr:', output);
    });
  
    this.hearProcess.on('close', (code) => {
      console.log(`gtasr 子进程退出，退出码 ${code}`);
    });
  }
  stopHearProcess() {
    this.translatedText = ''
    if (this.hearProcess) {
      console.log('Stopping hear process');
      this.hearProcess.kill('SIGTERM');
      this.hearProcess = null;
    } else {
      console.log('No hear process to stop');
    }
    if (this.takeOverHearProcess) {
      console.log('Stopping hear takeOverHearProcess process');
      this.takeOverHearProcess.kill('SIGTERM');
      this.takeOverHearProcess = null;
    }
  }

}
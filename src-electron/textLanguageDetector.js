/**
 * 文本语言检测工具类
 */

import { app } from 'electron'
import path from 'path'
const spawn = require('child_process').spawn

export default class TextLanguageDetector {
  constructor() {
    this.activeProcesses = new Map() // 存储活跃的检测进程
    this.processIdCounter = 0 // 进程ID计数器
  }

  /**
   * 检测文本语言
   * @param {string} text - 要检测的文本
   * @param {string[]} langs - 可选的语言列表，如 ['zh', 'en', 'ja', 'ko']
   * @returns {Promise<{dominant: {language: string, ratio: number}, ratios: object}>} 检测结果
   */
  async detectTextLanguage(text, langs = null) {
    return new Promise((resolve, reject) => {
      if (!text || text.trim().length === 0) {
        reject(new Error('文本内容不能为空'))
        return
      }

      const pkgPath = this.getDetectorPath()

      if (!pkgPath) {
        reject(new Error('当前平台不支持语言检测功能'))
        return
      }

      // 生成唯一的进程ID
      const processId = ++this.processIdCounter

      console.log(`[进程${processId}] 开始检测文本语言:`, text)
      console.log(`[进程${processId}] 指定语言列表:`, langs)
      console.log(`[进程${processId}] 检测工具路径:`, pkgPath)

      // 构建命令参数
      const args = ['--text', text]
      if (langs && langs.length > 0) {
        args.push('--langs')
        args.push(...langs)
      }

      console.log(`[进程${processId}] 执行命令参数:`, args)
      const detectProcess = spawn(pkgPath, args)

      // 将进程存储到活跃进程映射中
      this.activeProcesses.set(processId, detectProcess)

      let outputData = ''
      let errorData = ''

      // 清理进程的函数
      const cleanupProcess = () => {
        this.activeProcesses.delete(processId)
      }

      // 监听标准输出
      detectProcess.stdout.on('data', (data) => {
        outputData += data.toString()
      })

      // 监听错误输出
      detectProcess.stderr.on('data', (data) => {
        errorData += data.toString()
        console.error(`[进程${processId}] 语言检测错误输出:`, data.toString())
      })

      // 监听进程结束
      detectProcess.on('close', (code) => {
        console.log(`[进程${processId}] 语言检测进程退出，退出码: ${code}`)
        cleanupProcess()

        if (code === 0) {
          try {
            // 解析 JSON 结果
            const result = JSON.parse(outputData.trim())
            console.log(`[进程${processId}] 语言检测结果:`, result)
            resolve(result)
          } catch (error) {
            console.error(`[进程${processId}] 解析检测结果失败:`, error)
            reject(new Error('解析检测结果失败: ' + error.message))
          }
        } else {
          reject(new Error(`语言检测失败，退出码: ${code}, 错误信息: ${errorData}`))
        }
      })

      // 监听进程错误
      detectProcess.on('error', (error) => {
        console.error(`[进程${processId}] 启动语言检测进程失败:`, error)
        cleanupProcess()
        reject(new Error('启动语言检测进程失败: ' + error.message))
      })

      // 设置超时处理
      const timeoutId = setTimeout(() => {
        if (this.activeProcesses.has(processId)) {
          console.log(`[进程${processId}] 语言检测超时，终止进程`)
          detectProcess.kill('SIGTERM')
          cleanupProcess()
          reject(new Error('语言检测超时'))
        }
      }, 10000) // 10秒超时

      // 当进程正常结束时清除超时
      detectProcess.on('close', () => {
        clearTimeout(timeoutId)
      })
    })
  }

  /**
   * 获取检测工具路径
   * @returns {string|null} 检测工具路径，如果不支持当前平台则返回 null
   */
  getDetectorPath() {
    if (process.platform === 'darwin') {
      if (process.arch === 'arm64') {
        // macOS ARM64 版本
        return process.env.PROD 
          ? path.join(__statics, '../../lib/detect_lang/detect_lang')
          : path.join(app.getAppPath(), '../../lib/detect_lang/detect_lang')
      } else {
        // macOS Intel 版本 - 暂不支持
        return process.env.PROD 
          ? path.join(__statics, '../../lib/detect_lang_intel/detect_lang')
          : path.join(app.getAppPath(), '../../lib/detect_lang_intel/detect_lang')
      }
    } else if (process.platform === 'win32') {
      // Windows 版本 - 暂不支持
      return process.env.PROD 
          ? path.join(__statics, '../../lib/detect_lang_win/detect_lang_win.exe')
          : path.join(app.getAppPath(), '../../lib/detect_lang_win/detect_lang_win.exe')
    } else {
      // 其他平台 - 暂不支持
      console.warn('当前平台暂不支持语言检测')
      return null
    }
  }

  /**
   * 检查当前平台是否支持语言检测
   * @returns {boolean} 是否支持
   */
  isSupported() {
    return this.getDetectorPath() !== null
  }

  /**
   * 获取支持的平台信息
   * @returns {object} 平台支持信息
   */
  getSupportInfo() {
    return {
      currentPlatform: process.platform,
      currentArch: process.arch,
      isSupported: this.isSupported(),
      supportedPlatforms: [
        { platform: 'darwin', arch: 'arm64', description: 'macOS ARM64 (Apple Silicon)' }
      ]
    }
  }

  /**
   * 简单检测文本语言（兼容旧版本API）
   * @param {string} text - 要检测的文本
   * @returns {Promise<{language: string, confidence: number}>} 检测结果（旧格式）
   */
  async detectTextLanguageSimple(text) {
    try {
      const result = await this.detectTextLanguage(text)

      // 转换新格式到旧格式
      return {
        language: result.dominant.language,
        confidence: result.dominant.ratio
      }
    } catch (error) {
      throw error
    }
  }

  /**
   * 检测文本中多种语言的比例
   * @param {string} text - 要检测的文本
   * @param {string[]} langs - 语言列表，如 ['zh', 'en', 'ja', 'ko']
   * @returns {Promise<{dominant: {language: string, ratio: number}, ratios: object}>} 检测结果
   */
  async detectTextLanguageWithLangs(text, langs) {
    return await this.detectTextLanguage(text, langs)
  }

  /**
   * 停止所有检测进程
   */
  stopAllDetections() {
    console.log(`停止所有语言检测进程，当前活跃进程数: ${this.activeProcesses.size}`)
    for (const [processId, process] of this.activeProcesses) {
      console.log(`停止进程 ${processId}`)
      process.kill('SIGTERM')
    }
    this.activeProcesses.clear()
  }

  /**
   * 停止指定的检测进程
   * @param {number} processId - 进程ID
   */
  stopDetection(processId) {
    if (this.activeProcesses.has(processId)) {
      console.log(`停止语言检测进程 ${processId}`)
      this.activeProcesses.get(processId).kill('SIGTERM')
      this.activeProcesses.delete(processId)
    }
  }

  /**
   * 获取当前活跃的检测进程数量
   * @returns {number} 活跃进程数量
   */
  getActiveProcessCount() {
    return this.activeProcesses.size
  }

  /**
   * 获取所有活跃进程的ID列表
   * @returns {number[]} 进程ID列表
   */
  getActiveProcessIds() {
    return Array.from(this.activeProcesses.keys())
  }
}

/**自动更新 */
import { ipc<PERSON><PERSON>, BrowserWindow,dialog } from 'electron'
import { autoUpdater } from "electron-updater"
export default class AutoUpdaterWrapper {
    constructor(mainWindow,autoUpdater) {
        this.mainWindow = mainWindow
        this.autoUpdater = autoUpdater
    }
    updateHandle() {
        console.log('start check update flag ',this.flag)
        autoUpdater.checkForUpdatesAndNotify();
    }
    sendUpdateMessage(index, info) {
        this.mainWindow.webContents.send('message', { index, info })
    }

    set setFlag(flag){
        this.flag = flag
    }
    get setFlag(){
        return this.flag
    }

    start() {
        console.log('auto update start')
        let message = {
            error: 0,
            checking: 1,
            updateAva: 2,
            updateNotAva: 3,
        };
        this.flag = false
        autoUpdater.autoDownload = false
        autoUpdater.on('error', (error) => {
            this.sendUpdateMessage(message.error,error)
        });
        //正在检查
        autoUpdater.on('checking-for-update',() =>{
            this.sendUpdateMessage(message.checking)
        });
        //正在下载
        autoUpdater.on('update-available',  (info) =>{
            this.sendUpdateMessage(message.updateAva)
        });
        //当前是最新版本
        autoUpdater.on('update-not-available',() => {
            console.log('update-not-available flag ',this.flag)
            if (this.flag) {
                this.sendUpdateMessage(message.updateNotAva)
                this.flag = false
            }
        });
        // 更新下载进度事件
        autoUpdater.on('download-progress',  (progressObj)=> {
            this.mainWindow.webContents.send('downloadProgress', progressObj)
        })
        autoUpdater.on('update-downloaded', (event, releaseNotes, releaseName, releaseDate, updateUrl, quitAndUpdate)=>{
            ipcMain.on('isUpdateNow', (e, arg) => {
                autoUpdater.quitAndInstall();
            });
            this.mainWindow.webContents.send('isUpdateNow')
        });
        ipcMain.on('startUpdate', (e, arg) => {
            autoUpdater.downloadUpdate()
        })
        ipcMain.on("checkUpdate", () => {
            this.flag = true
            this.updateHandle()
        })
    }
}

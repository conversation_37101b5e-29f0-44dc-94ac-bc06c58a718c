/**
 * 文本翻译新开单独窗口
 */
import { ipcMain, app, BrowserWindow, dialog } from 'electron'
import path from 'path'
import Store from 'electron-store'

const chokidar = require('chokidar');
const fs = require('fs');
let store = new Store();
const HTMLtoDOCX = require('html-to-docx');

export default class TranslateWrapper {
    constructor(mainWindow) {
        this.mainWindow = mainWindow;
    }

    start() {
        ipcMain.on('openTranslateWindow', (e, arg) => {
            if(this.childWindow == null){
                this.openTranslateWindow()
            }
        })

        //开始监听一些ipc信息
        //获取文件夹事件 开始
        //可获取的文件后缀
        //监听获取根目录
        ipcMain.handle('get-root-folder', async () => {
            if (store.has('translateFolder')) {
                const folderPath = store.get('translateFolder')
                console.log('rootFolder ', folderPath);
                this.watchRootFolder(folderPath)
                return [{
                    id: 'root',
                    name: path.basename(folderPath), // 根目录名称
                    path: folderPath,                // 根目录路径
                    type: 'directory',               // 标记为目录类型
                    children: this.buildFileTree(folderPath) // 递归获取子文件和文件夹
                }]
            }
        })

        ipcMain.handle('select-folder', async () => {
            const result = await dialog.showOpenDialog({
                properties: ['openDirectory'],
            });
            if (result.canceled) return null;
            const folderPath = result.filePaths[0];
            store.set('translateFolder', folderPath)
            this.watchRootFolder(folderPath);
            return [{
                id: 'root',
                name: path.basename(folderPath), // 根目录名称
                path: folderPath,                // 根目录路径
                type: 'directory',               // 标记为目录类型
                children: this.buildFileTree(folderPath) // 递归获取子文件和文件夹
            }]
        });

        // 选择对比文件
        ipcMain.handle('select-contrast-file', async () => {
            const res = await dialog.showOpenDialog({
                filters: [
                    { name: 'Text Files', extensions: ['docx'] },
                ],
                properties: ['openFile']  // 只允许选择文件，不允许选择文件夹
            })
            return res.filePaths[0]
        })

        // 获取文件夹事件 结束
        //解析文件内容
        ipcMain.handle('read-file', async (event, path) => {
            try {
                return fs.readFileSync(path)
            } catch (err) {
                return false
            }
        })
        // 删除指定文件
        ipcMain.handle('delete-file', async (event, path) => {
            return fs.unlinkSync(path)
        })
        //写入文件内容
        ipcMain.handle('write-file', async (event, path, htmlString) => {
            const fileBuffer = await HTMLtoDOCX(htmlString, null, {
                table: { row: { cantSplit: true } },
                footer: true,
                pageNumber: true,
                preprocessing: { skipHTMLMinify: false }
            });
            return fs.writeFileSync(path, fileBuffer)
        })

    }

    openTranslateWindow(){
        if(this.childWindow){
            this.childWindow.focus()
            return
        }
        console.log('openTranslateWindow ')
        this.childWindow = new BrowserWindow({
            width: 800,
            height: 600,
            useContentSize: true,
            frame: true, // 是否隐藏标题栏
            webPreferences: {
                devTools: true, // 隐藏调试工具
                nodeIntegration: true, //在网页中集成Node
                enableRemoteModule: true, // 打开remote模块
                contextIsolation: false
            },
            x:250,
        });
        this.childWindow.setMenu(null);
        this.childWindow.show();
        this.childWindow.loadURL(process.env.APP_URL + '#/fileTranslate');
        if(process.env.NODE_ENV === 'development'){
            this.childWindow.webContents.openDevTools()
        }
        this.childWindow.on('show', () => {
        })
        this.childWindow.on('closed', () => {
            this.unwatchRootFolder()
            this.childWindow = null
            try {
                this.mainWindow.webContents.send('closeTranslateWindow', null)
            } catch (error) {
            }
        })
    }
    unwatchRootFolder() {
        if (this.watchPath) {
            this.watchPath.close();
            this.watchPath = null;
        }
    }
    //监听文件变动
    watchRootFolder(folderPath) {
        // 监听文件夹变动 开始
        if (this.watchPath) this.watchPath.close()
        // 监视指定目录及其子目录中的所有变化
        this.watchPath = chokidar.watch(folderPath, {
            ignored: /(^|[\/\\])\../,
            persistent: true, // 持续监视
            ignoreInitial: true
        }).on('add', () => {
            const files = [{
                id: 'root',
                name: path.basename(folderPath), // 根目录名称
                path: folderPath,                // 根目录路径
                type: 'directory',               // 标记为目录类型
                children: this.buildFileTree(folderPath) // 递归获取子文件和文件夹
            }]
            this.childWindow.webContents.send('folder-changed', { files });
        }).on('unlink', () => {
            const files = [{
                id: 'root',
                name: path.basename(folderPath), // 根目录名称
                path: folderPath,                // 根目录路径
                type: 'directory',               // 标记为目录类型
                children: this.buildFileTree(folderPath) // 递归获取子文件和文件夹
            }]
            this.childWindow.webContents.send('folder-changed', { files });
        })
    }

    close(){
        if(this.childWindow){
            this.childWindow.close()
            this.childWindow = null
        }
    }

    buildFileTree(dir) {
        const files = fs.readdirSync(dir);
        const allowedExtensions = ['docx', 'pptx', 'xlsx', 'odt', 'odp', 'ods', 'pdf'];
        return files
            .map(file => {
                const filePath = path.join(dir, file);
                const isDirectory = fs.statSync(filePath).isDirectory();
                const ext = path.extname(file).slice(1).toLowerCase(); // 获取文件扩展名（不含点号）
                // 递归处理文件夹，过滤非文件夹的指定后缀文件
                if (isDirectory) {
                    return {
                        name: file,
                        id: filePath,
                        path: filePath,
                        type: 'directory',
                        children: this.buildFileTree(filePath), // 递归获取子文件夹
                    };
                } else if (allowedExtensions.includes(ext)) {
                    return {
                        id: filePath,
                        name: file,
                        path: filePath,
                        type: 'file',
                    };
                }
                return null;
            })
            .filter(item => item !== null); // 过滤掉未包含在允许列表中的文件
    }

}

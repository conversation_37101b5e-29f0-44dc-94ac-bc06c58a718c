/**
 * 文本翻译新开单独窗口
 */
import { ipcMain, app, BrowserWindow, dialog,screen } from 'electron'
import path from 'path'
import Store from 'electron-store'
let store = new Store();

export default class RealtimeTranslateWindow {
    constructor(mainWindow) {
        this.mainWindow = mainWindow;
    }

    start() {
        ipcMain.on('openRealTimeTranslateWindow', (e, arg) => {
            if(this.childWindow == null){
                this.openRealTimeTranslateWindow()
            }
        })
        ipcMain.on('closeRealTimeTranslateWindow', (e, arg) => {
            this.close()
        })
        
        ipcMain.on('realtime-translate-data',(e,arg)=>{
            console.log(arg)
            if(this.childWindow){
                this.childWindow.webContents.send('realtime-asr-data', arg);
            }
        })
        ipcMain.on('speaker-hide-status-change',(e,arg)=>{
            console.log(arg)
            if(this.childWindow){
                this.childWindow.webContents.send('hide-speaker-change', arg);
            }
        })

        ipcMain.on('change-opacity',(e,arg)=>{
            console.log(arg)
            this.childWindow.setOpacity(arg);
        })
        ipcMain.on('translateWindowStaytopClick',(e,arg)=>{
            this.childWindow.setAlwaysOnTop(arg);
        })

        // 处理滚动同步事件
        ipcMain.on('sync-scroll',(e,arg)=>{
            console.log('sync-scroll received:', arg)
            if(this.childWindow){
                this.childWindow.webContents.send('scroll-sync', arg);
            }
        })
        ipcMain.on('closeTranslateWindow',(e,arg)=>{
            this.close()
        })
        
    }

    stopAsr(){
        if(this.childWindow == null){
            return
        }
        this.childWindow.webContents.send('stopAsr', null);
    }

    openRealTimeTranslateWindow(){
        if(this.childWindow){
            this.childWindow.focus()
            return
        }
        const primaryDisplay = screen.getPrimaryDisplay();
        const screenWidth = primaryDisplay.workArea.width;
        const screenX = primaryDisplay.workArea.x;
        const screenY = primaryDisplay.workArea.y;
        const offsetX = 100;
        const offsetY = 0;

        this.childWindow = new BrowserWindow({
            width: 800,
            height: 300,
            useContentSize: true,
            x: screenX + screenWidth - 800 - offsetX, // 窗口右对齐
            y: screenY + offsetY,
            frame: false, // 是否隐藏标题栏
            show: false,
            webPreferences: {
                devTools: true, // 隐藏调试工具
                nodeIntegration: true, //在网页中集成Node
                enableRemoteModule: true, // 打开remote模块
                contextIsolation: false
            },
            x:250,
        });
        this.childWindow.setOpacity(0.5);
        this.childWindow.setMenu(null);
        this.childWindow.show();
        this.childWindow.loadURL(process.env.APP_URL + '#/realtimeTranslate');
        if(process.env.NODE_ENV === 'development'){
            this.childWindow.webContents.openDevTools()
        }
        this.childWindow.on('show', () => {
        })
        this.childWindow.once('ready-to-show', () => {
            this.childWindow.show();
        });
        this.childWindow.on('closed', () => {
            this.childWindow = null
            try {
                this.mainWindow.webContents.send('closeRealtimeTranslateWindow', null)
            } catch (error) {
            }
        })
    }
    close(){
        if(this.childWindow){
            this.childWindow.close()
            this.childWindow = null
        }
    }

}

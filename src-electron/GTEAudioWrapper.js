/**降噪主进程使用 */
import { ipcMain, app, BrowserWindow} from 'electron'
const macAudioDevices = require('@spotxyz/macos-audio-devices');
import { setDefaultOutputDevice } from '@spotxyz/macos-audio-devices'
import Store from 'electron-store'
const path = require('path')
const fs = require('fs');
const exec = require('child_process').exec
const { spawn } = require('child_process');
var sudo = require('sudo-prompt');
import volume from '../src/utils/controlMacMicVolume'
import { getWinMic, setWinMic, getWinOutput, setWinOutput } from '@/utils/controlWinMicVolume'
let soxProcess;
let denoiser
let userDeviceOutputVolume
if (process.env.PROD) {
    global.__statics = __dirname
  }
if (process.platform == 'darwin') {
    let _path = '../../lib/pb-mac/'
    denoiser = process.env.PROD ? path.join(__statics, _path) : path.join(app.getAppPath(), _path)
}else{
    let _path = '../../lib/pb-win'
    denoiser = process.env.PROD ? path.join(__statics, _path) : path.join(app.getAppPath(), _path)
}
const winDriver = 'gtassist.exe';
const macDrive = './gtassist'
export default class GTEAudioWrapper {
    constructor(mainWindow) {
        this.mainWindow = mainWindow;
    }

    start() {
        this.store = new Store();
        this.SoundVolumeView = process.env.PROD ? path.join(__statics, '../../lib/SoundVolumeView.exe') :
        path.join(app.getAppPath(), '../../lib/SoundVolumeView.exe')
        console.log('SoundVolumeView ',this.SoundVolumeView)
        this.GetNir = process.env.PROD ? path.join(__statics, '../../lib/GetNir.exe') :
        path.join(app.getAppPath(), '../../lib/GetNir.exe')
        //设置默认
        ipcMain.on("setAsDefault", (event, value) => {
            this.setAsDefault(value)
        })
        //获取音频输出设备
        ipcMain.handle("getSystemOutputDevices", async (event, value) => {
            let devices = await this.getAudioOutputDevices()
            return devices
        })
        //获取音频输出设备
        ipcMain.handle("getSystemInputDevices", async (event, value) => {
            let devices = await this.getAudioInputDevices()
            return devices
        })
        ipcMain.handle('getAudioDriver',(event,value)=>{
            return "GTEAudio"
        })
        //安装denoiser
        ipcMain.on('installGTEAudio',(e,arg)=>{
            this.installGTEAudio()
        })
    }

    installGTEAudio(){
        if (process.platform == 'darwin') {
            let pkgpPath = process.env.PROD ? path.join(__statics,'../../lib/GTEAudio.pkg'):
            path.join(app.getAppPath(),'../../lib/GTEAudio.pkg')
            exec('open ' + pkgpPath)
        }else{
            let _path = '../../lib/GTEAudio/'
            let folder = process.env.PROD ? path.join(__statics, _path) : path.join(app.getAppPath(), _path)
            const folderPath = `"${folder}"`;
            let order = `cd ${folderPath}  && activation.exe install GTEAudio.inf root\\GTEAudio`
            const startArgs = ['install', 'GTEAudio.inf', 'root\\GTEAudio'];
            var options = {
                name: 'TerpMeta',
            };
            sudo.exec(order, options,
                function(error, stdout, stderr) {
                    if (error) throw error;
                    if(stdout){
                    }
                    console.log('stdout: ' + stdout);
                }
            );
        }
    }
    getWinInputGTEAudioName(){
        return new Promise(async (resolve, reject) => {
            let input = await this.getWinSystemInputDevice()
            input.forEach(item=>{
              if(item.deviceName.includes('GTEAudio')){
                let virtualDriverName =  item.name + ' (' + item.deviceName + ')'
                resolve(virtualDriverName)
              }
            })
        })
    }
    getWinOutputGTEAudioName(){
        return new Promise(async (resolve, reject) => {
            let input = await this.getWinSystemOutputDevice()
            input.forEach(item=>{
              if(item.deviceName.includes('GTEAudio')){
                let virtualDriverName =  item.name + ' (' + item.deviceName + ')'
                resolve(virtualDriverName)
              }
            })
        })
    }
    async changeOutputDevice2GreenTerp(){
        let GTEAudio = 'GTEAudio'
        if(process.platform !== 'darwin'){
            GTEAudio = await this.getWinOutputGTEAudioName()
            userDeviceOutputVolume =  getWinOutput()
        }else{
            userDeviceOutputVolume =  await volume.getOutputValue()
        }
        let devices = await this.getAudioOutputDevices()
        for (const output of devices) {
            console.log(output.fullName)
            if(output.fullName.includes(GTEAudio)){
              console.log('changeOutputDevice2GreenTerp deviceName ',output.deviceName)
              this.setAsDefault(output.id);
              break
            }
        }
    }
    stopSoxProcess(){
        console.log('stop sox process')
        if (soxProcess) {
            soxProcess.kill();
        }
    }
     //恢复默认输出到用户选择
     changeSystemOutputToDefault() {
        return new Promise(async (resolve, reject) => {
            try {
                console.log('==========');
                if (soxProcess) {
                    soxProcess.kill();
                }
                let devices = await this.getAudioOutputDevices();
                const res = this.getDefaultOutputDeviceInfo();
                console.log('res ', res);
                if (!res) {
                    return resolve();
                }
                for (const output of devices) {
                    console.log('output device name: ', output.deviceName, ' name: ', output.name);
                    console.log(res.output.includes(output.deviceName) && res.output.includes(output.name));
                    if (res.output.includes(output.deviceName) && res.output.includes(output.name)) {
                        await this.setAsDefault(output.id);
                        if (process.platform === 'darwin') {
                            volume.setOutputValue(userDeviceOutputVolume);
                        } else {
                            setWinOutput(userDeviceOutputVolume);
                        }
                        return resolve();
                    }
                }
                resolve();
            } catch (error) {
                reject(error);
            }
        });
    } 

    getDefaultOutputDeviceInfo(){
        const deviceInfo = this.store.get('interpretease-audio-output')
        console.log('deviceInfo ',deviceInfo)
        if(deviceInfo){
            let outputChoose = deviceInfo
            let output = null
            if(outputChoose.includes(' - ')){
              // Communications - 耳机 (Realtek(R) Audio)
              const splitArray = outputChoose.split(' - ');
              if (splitArray[0].includes('Default')) {
                output = splitArray.slice(1).join(' - ');
              }else{
                output = outputChoose
              }
            }else{
              output = outputChoose
            }
            return {output}
        }
        return null
    }
    
    extractFirstWord(str) {
        let index = str.indexOf(' ');
        if (index!== -1) {
            return str.substring(0, index);
        }
        return str;
    }

    async getSystemSpeakerVolume() {
        if (soxProcess) {
            if (process.platform == 'darwin') {
                return new Promise((resolve, reject) => {
                    let inputAudio = process.env.PROD ? path.join(__statics, '../../lib/osx-audio-input') : path.join(app.getAppPath(), '../../lib/osx-audio-input');
                    let inputAudioArgs = ['get', this.realOutputDevice, 'output'];
                    const child = spawn(inputAudio, inputAudioArgs);
                    let outputData = '';
                    // 监听标准输出
                    child.stdout.on('data', (data) => {
                        const dataStr = data.toString();
                        console.log(`标准输出: ${dataStr}`);
                        outputData += dataStr;
                    });
                    
                    // 监听错误输出
                    child.stderr.on('data', (data) => {
                        console.error(`错误输出: ${data.toString()}`);
                    });
                    
                    // 监听子进程关闭事件
                    child.on('close', (code) => {
                        console.log(`子进程已退出，退出码: ${code}`);
                        if (code === 0) {
                            const volumeValue = parseFloat(outputData.trim()) * 100;
                            resolve(volumeValue);
                        } else {
                            // 执行失败
                            reject(new Error(`子进程执行失败，退出码: ${code}`));
                        }
                    });
                    
                    // 处理可能的错误
                    child.on('error', (err) => {
                        reject(err);
                    });
                });
            } else {
                return getWinOutput();
            }
        } else {
            if (process.platform == 'darwin') {
                let value = await volume.getOutputValue();
                return value;
            } else {
                return getWinOutput();
            }
        }
    }

    changeLoudSpeakerValue(volumeValue){
        if(soxProcess){
            if(process.platform !== 'darwin'){
                let device = this.extractFirstWord(this.realOutputDevice)
                //调整real output device
                const soundVolumeViewPath = `"${this.SoundVolumeView}"`;
                const command = `${soundVolumeViewPath} /SetVolume "${device}" ${volumeValue}`;
                exec(command, (error, stdout, stderr) => {})
            }else{
                //TODO
                console.log('changeLoudSpeakerValue ',this.realOutputDevice)
                let inputAudio = process.env.PROD ? path.join(__statics, '../../lib/osx-audio-input') : path.join(app.getAppPath(), '../../lib/osx-audio-input');
                let inputAudioArgs = ['set',this.realOutputDevice, volumeValue / 100,'output'];
                const child = spawn(inputAudio, inputAudioArgs);
                // 监听标准输出
                child.stdout.on('data', (data) => {
                    console.log(`标准输出: ${data.toString()}`);
                });
                // 监听错误输出
                child.stderr.on('data', (data) => {
                    console.error(`错误输出: ${data.toString()}`);
                });
                // 监听子进程关闭事件
                child.on('close', (code) => {
                    console.log(`子进程已退出，退出码: ${code}`);
                });
            }
        }else{
            if(process.platform !== 'darwin'){
                //调整系统默认
                setWinOutput(volumeValue)
            }else{
                volume.setOutputValue(volumeValue);
            }
            
        }
    }

    //从GTE进，从用户选择出
    changeSystemOutputFromGTE(input, output,volumeValue) {
        if(!volumeValue){
            volumeValue = 60
        }
        return new Promise((resolve, reject) => {
            if (soxProcess) {
                soxProcess.kill();
            }
            this.realOutputDevice = output
            const args = [
                '-B',
                '-t', process.platform === 'darwin' ? 'coreaudio' : 'waveaudio', input,
                '-t', process.platform === 'darwin' ? 'coreaudio' : 'waveaudio', output
            ];
    
            console.log('pb order ',args)
    
            const driver = process.platform === 'darwin' ? macDrive : winDriver;
            const options = { encoding: 'utf-8', cwd: denoiser };
    
            soxProcess = spawn(driver, args, options);
            if(process.platform === 'darwin'){
                volume.setOutputValue(volumeValue)
            }else{
                setWinOutput(volumeValue)
            }
            soxProcess.stderr.on('data', async (data) => {
                let _data = `${data}`
                console.error(`SoX error: ${data}`);
                let logInfo = {
                    source: 'TM',
                    descInfo: 'GTE changeSystemOutputFromGTE Sox Error',
                    message: `SoX error: ${data}`,
                    extraField1: 'order:' + args,
                    extraField2: '-' + await this.getAudioOutputDevices() + '-'
                }
                if(_data.includes('./gtassist FAIL')){
                    this.mainWindow.webContents.send('soxerror',logInfo)
                }
                resolve('SoX process completed successfully');
            });
    
            soxProcess.on('close', (code) => {
                if (code !== 0) {
                    reject(new Error(`SoX process exited with code ${code}`));
                } else {
                    resolve('SoX process completed successfully');
                }
            });
    
            soxProcess.on('error', (error) => {
                console.error(`SoX error 2: ${error}`);
                reject(error);
            });
        });
    }

    getValidId(output) {
        const id = typeof output === 'string' ? output : output.id;
        if (!id || typeof id !== 'string' || id.length === 0) {
            throw new Error('invalid output id: ' + id);
        }
        return id;
    }

    async setAsDefault(output, type = 'multimedia') {
        if (process.platform == 'darwin') {
            console.log('mac set as default ',output)
            await setDefaultOutputDevice(output)
        }else{
            const DefaultTypes = {
                all: 'all',
                multimedia: 1,
                communications: 2,
            };
            const id = this.getValidId(output);
            if (['all', 'multimedia', 'communications'].includes(type) === false) {
                throw new Error('invalid default type: ' + type);
            }
            const defaultType = DefaultTypes[type];
            const soundVolumeViewPath = `"${this.SoundVolumeView}"`;
            const order = `${soundVolumeViewPath} /SetDefault ${id} ${defaultType}`
            exec(order, (error, stdout, stderr) => {
    
            })
        }
    }
    getAudioInputDevices(){
        if (process.env.PROD) {
            global.__statics = __dirname
        }
        if (process.platform == 'darwin') {
            return this.getMacSystemInputDevice()
        }else{
            return this.getWinSystemInputDevice()
        }
    }

    getAudioOutputDevices() {
        if (process.env.PROD) {
            global.__statics = __dirname
        }
        if (process.platform == 'darwin') {
            return this.getMacSystemOutputDevice()
        }else{
            return this.getWinSystemOutputDevice()
        }
        
    }

    getMacSystemInputDevice(){
        return new Promise((resolve,reject) =>{
            const inputDevices = macAudioDevices.getInputDevices.sync();
            for (const device of inputDevices) {
                // 这里可以放置你对每个设备的处理逻辑
                device.deviceName = device.name
            }
            resolve(inputDevices)
        })
    }

    getMacSystemOutputDevice() {
        return new Promise((resolve,reject) =>{
            const outputDevices = macAudioDevices.getOutputDevices.sync();
            for (const device of outputDevices) {
                // 这里可以放置你对每个设备的处理逻辑
                device.deviceName = device.name
                device.fullName = device.name
            }
            resolve(outputDevices)
        })
    }
    getWinSystemInputDevice() {
        // audio device
        return new Promise((resolve, reject) => {
            // 使用 SoundVolumeView 和 GetNir 命令获取音频输入设备信息
            const soundVolumeViewPath = `"${this.SoundVolumeView}"`;
            const getNirPath = `"${this.GetNir}"`;
            const command = `${soundVolumeViewPath} /stab "" | ${getNirPath} "Item ID, Name, Device Name, Default Multimedia, Default Communications, Muted, Volume Percent" "Type=Device && Direction=Capture && DeviceState=Active"`;
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                // 解析命令输出
                const devices = [];
                const lines = stdout.split('\r\n');
                for (const line of lines) {
                    const [id, name, deviceName, isDefaultMultimedia, isDefaultCommunications, isMuted, volume] = line.split('\t');
                    devices.push({
                        id,
                        name,
                        deviceName,
                        fullName: name+' (' + deviceName+')',
                        isDefaultMultimedia: isDefaultMultimedia === 'Capture',
                        isDefaultCommunications: isDefaultCommunications === 'Capture',
                        isMuted: isMuted === 'Yes',
                        volume: Number(volume.replace('%', '')),
                    });
                }
    
                resolve(devices);
            });
        });
    }
        

    getWinSystemOutputDevice(){
        //audio device
        return new Promise((resolve, reject) => {
            // 使用 SoundVolumeView 和 GetNir 命令获取音频输出设备信息
            const soundVolumeViewPath = `"${this.SoundVolumeView}"`;
            const getNirPath = `"${this.GetNir}"`;
            // const command = this.SoundVolumeView + ' /stab "" | ' + this.GetNir + ' "Item ID, Name, Device Name, Default Multimedia, Default Communications, Muted, Volume Percent" "Type=Device && Direction=Render && DeviceState=Active"';
            const command = `${soundVolumeViewPath} /stab "" | ${getNirPath} "Item ID, Name, Device Name, Default Multimedia, Default Communications, Muted, Volume Percent" "Type=Device && Direction=Render && DeviceState=Active"`;
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                // 解析命令输出
                const devices = [];
                const lines = stdout.split('\r\n');
                for (const line of lines) {
                    const [id, name, deviceName, isDefaultMultimedia, isDefaultCommunications, isMuted, volume] = line.split('\t');
                    devices.push({
                        id,
                        name,
                        deviceName,
                        fullName: name+' (' + deviceName+')',
                        isDefaultMultimedia: isDefaultMultimedia === 'Render',
                        isDefaultCommunications: isDefaultCommunications === 'Render',
                        isMuted: isMuted === 'Yes',
                        volume: Number(volume.replace('%', '')),
                    });
                }

                resolve(devices);
            });
        });
    }

}
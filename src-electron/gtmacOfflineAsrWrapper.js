/**
 *  greenterp offline asr
 */

import { app } from 'electron'
import path from 'path'
const spawn = require('child_process').spawn

export default class GTMacOfflineAsrWrapper {
  constructor(mainWindow) {
    this.mainWindow = mainWindow
    this.gtasrProcess = null
  }


  startGreenTerpOfflineAsr(req) {
    console.log('startGreenTerpOfflineAsr ',req)
    //{ device: 'MacBook Pro麦克风', language: 'zh-CN', useOnlineAsr: false }
    let pkgpPath = null
    let rootPath = null
    if (process.platform == 'darwin') {
      if(process.arch == 'arm64'){
        pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gt_offline_asr/gt_offline_asr') : path.join(app.getAppPath(), '../../lib/gt_offline_asr/gt_offline_asr');
      }else{
        pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gt_offline_asr_intel/gt_offline_asr_intel') : path.join(app.getAppPath(), '../../lib/gt_offline_asr_intel/gt_offline_asr_intel');
      } 
      
      rootPath = app.getPath('userData') + '/AsrLanguagePacks/'
    }else{
      pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gt_offline_asr_win/gt_offline_asr_win.exe') : path.join(app.getAppPath(), '../../lib/gt_offline_asr_win/gt_offline_asr_win');
      rootPath = app.getPath('userData') + '\\AsrLanguagePacks\\';
    }
    
    // let args = ['/Users/<USER>/Library/Application\ Support/Google/Chrome/SODALanguagePacks/cmn-Hans-CN/1.3054.0/SODAModels/','GTEAudio 2ch'];
    //let args = ['/Users/<USER>/Library/Application\ Support/Google/Chrome/SODALanguagePacks/cmn-Hans-CN/1.3054.0/SODAModels/',req.device];
    let args = [rootPath + req.language, req.device];
    console.log('Executing command:', pkgpPath, args.join(' '));
    this.gtasrProcess = spawn(pkgpPath, args);
    this.gtasrProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('data:', output);
  
      const handleResult = (prefix, resultType) => {
          if (output.startsWith(prefix)) {
              const result = output.substring(prefix.length);
              let newResult = result;
              if(req.language === 'zh-CN'){
                newResult = result.replace(/\s/g, '');  
              }
              const index = newResult.indexOf('SpeechRecognizing');
              if (index!== -1) {
                newResult = newResult.slice(0, index);
              }
              const asrResult = {
                  "result": newResult,
                  "speakingRate": 0,
                  "type": resultType
              };
              console.log(asrResult)
              if (this.mainWindow && this.mainWindow.webContents) {
                  this.mainWindow.webContents.send('asrResult', { type: resultType, data: JSON.stringify(asrResult) });
              }
          }
      };
  
      handleResult('SpeechRecognized:', 'speechRecognized');
      handleResult('SpeechRecognizing:', 'speechRecognizing');
  });
    
    this.gtasrProcess.stderr.on('data', (data) => {
        const output = data.toString();
        console.error('stderr:', output);
    });
    
    this.gtasrProcess.on('close', (code) => {
        console.log(`online gtasr 子进程退出，退出码 ${code}`);
    });
    
    this.gtasrProcess.on('error', (err) => {
        console.error('Failed to start child process:', err);
    });
  }
  
  stopGreenTerpOfflineAsr() {
    if (this.gtasrProcess) {
      console.log('Stopping gtasr process');
      this.gtasrProcess.kill('SIGTERM');
      this.gtasrProcess = null;
    } else {
      console.log('No gtast process to stop');
    }
  }
}
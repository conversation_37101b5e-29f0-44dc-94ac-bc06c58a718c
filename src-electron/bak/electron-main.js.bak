import {app, BrowserWindow, dialog, ipc<PERSON>ain, Menu} from 'electron'
import path from 'path'
import os from 'os'
import {getGTAVersion, quitGtAudio} from "../src/utils/macControl"

const exec = require('child_process').exec
const spawn = require('child_process').spawn
const HTMLtoDOCX = require('html-to-docx');
const fs = require('fs');
const chokidar = require('chokidar');

// const vosk = require("vosk");
// var mic = require("mic");
//mac在线模式持续的时长
let macOnlineModeInterval = 0
let timeInterval = null
//新的进程是接管，还是不是接管
let newProcessIsTakeOver = false
const SAMPLE_RATE = 16000;
let hearProcess = null
let takeOverHearProcess = null
function startTime(){
  macOnlineModeInterval = 0
  timeInterval = setInterval(() => {
    macOnlineModeInterval++
    console.log(macOnlineModeInterval)
    //8分钟后开启新的进程，进行接管
    if(8 * 60 == macOnlineModeInterval){
      if(hearProcess){
        console.log('## start takeover online process')
        startTakeOverOnlineHearProcess(macModeReq)
        newProcessIsTakeOver = true
      }else if(takeOverHearProcess){
        startOnlineHearProcess(macModeReq)
        newProcessIsTakeOver = false
        console.log('## start online process')
      }
    }
  }, 1000);
}

function restartTime(){
  stopTime()
  startTime()
}

function stopTime(){
  clearInterval(timeInterval)
}

// needed in case process is undefined under Linux
const platform = process.platform || os.platform()
if (process.env.PROD) {
  global.__statics = __dirname
}

let deeplxprocess = null
let lastAsrResult = null
let micInstance = null
let micInputStream = null
let model = null
let recognizer = null
let soxProcess = null
let soxPath = process.env.PROD ? path.join(__statics, '../../lib') : path.join(app.getAppPath(), '../../lib');
//设置音频输入设备音量
ipcMain.on('setInputDeviceVolume', (event, req) => {
  let inputAudio = process.env.PROD ? path.join(__statics, '../../lib/osx-audio-input') : path.join(app.getAppPath(), '../../lib/osx-audio-input');
  let inputAudioArgs = [req.deviceName, req.volume];
  spawn(inputAudio, inputAudioArgs);
})
let macModeReq = null
ipcMain.on('startAsr', async (event, req) => {
  if (hearProcess) {
    stopHearProcess()
  }
  macModeReq = req
  // startVosk(req)
  startOnlineHearProcess(req)
  // startOfflineHearProcess(req)
});


ipcMain.on('stopAsr', (event) => {
  stopTime()
  stopVosk()
  stopHearProcess()
});

ipcMain.on("staytopClick", (event, value) => {
  mainWindow.setAlwaysOnTop(value);
})
ipcMain.on("pinClick", (event, value) => {
  mainWindow.setMovable(!value)
})
function startVosk(req) {
  // vosk
  vosk.setLogLevel(0);
  let MODEL_PATH = ""
  if (req.language == "zh-CN") {
    MODEL_PATH = process.env.PROD ? path.join(__statics, '../../lib/vosk-model-cn-0.22') : path.join(app.getAppPath(), '../../lib/vosk-model-cn-0.22');
    // MODEL_PATH = process.env.PROD ? path.join(__statics, '../../lib/vosk-model-small-cn-0.22') : path.join(app.getAppPath(), '../../lib/vosk-model-small-cn-0.22');
  } else {
    MODEL_PATH = process.env.PROD ? path.join(__statics, '../../lib/vosk-model-small-en-us-0.15') : path.join(app.getAppPath(), '../../lib/vosk-model-small-en-us-0.15');
    // MODEL_PATH = process.env.PROD ? path.join(__statics, '../../lib/vosk-recasepunc-en-0.22') : path.join(app.getAppPath(), '../../vosk-recasepunc-en-0.22');
  }

  model = new vosk.Model(MODEL_PATH);
  recognizer = new vosk.Recognizer({ model: model, sampleRate: SAMPLE_RATE });

  // 使用 sox 从指定设备录制音频
  // const deviceName = 'GTEAudio 2ch';
  const deviceName = req.device
  const args = [
    '--default-device',
    // '-t', 'coreaudio',deviceName,
    '--no-show-progress',
    '--rate', 16000,
    '--channels', 1,
    '--encoding', 'signed-integer',
    '--bits', '16',
    '--type', 'wav',
    '-'
  ];

  const driver = './gtrecord';
  const options = { encoding: 'utf-8', cwd: soxPath };
  soxProcess = spawn(driver, args, options);

  soxProcess.stdout.on('data', data => {
    // console.log('data ',data)
    let resp = {}
    if (recognizer.acceptWaveform(data)) {
      // console.log('result:',rec.result().text);
      resp.result = recognizer.result().text
      resp.type = 'speechRecognized'
    } else {
      // console.log('partial:',rec.partialResult().partial);
      resp.result = recognizer.partialResult().partial
      resp.type = 'speechRecognizing'
    }
    if (resp.result) {
      console.log('resp ', resp)
      if (req.language == "zh-CN") {
        resp.result = resp.result.replace(/\s+/g, '')
      }
      mainWindow.webContents.send('asrResult', { type: 'speechRecognized', data: JSON.stringify(resp) });
    }
  });
  soxProcess.on('error', err => {
    console.error('Failed to start subprocess:', err);
  });
  soxProcess.on('close', (code) => {
    console.log(`sox process closed with code ${code}`);
    // Restart sox process if necessary
  });
}
function stopVosk() {
  if (soxProcess) {
    soxProcess.kill();
    soxProcess = null;
    // if (recognizer) recognizer.free();
    // if (model) model.free();
  }
}
function startOnlineHearProcess(req) {
  let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gtasr') : path.join(app.getAppPath(), '../../lib/gtasr');
  let args = ['-l', req.language, '-p', '-w', req.device];
  // let args = ['-l', 'zh-CN', '-p', '-w', 'MacBook Pro麦克风'];
  console.log('Executing command:', pkgpPath, args.join(' '));
  hearProcess = spawn(pkgpPath, args);
  //开始计时
  restartTime()
  hearProcess.stdout.on('data', (data) => {
    //停掉接管进程
    if(!newProcessIsTakeOver && takeOverHearProcess){
      takeOverHearProcess.kill('SIGTERM');
      takeOverHearProcess = null;
      console.log('kill hearprocess ',takeOverHearProcess)
    }
    const output =data.toString()
    if(mainWindow && mainWindow.webContents){
      mainWindow.webContents.send('asrResult', { type: 'speechRecognized', data: output });
    }
  });

  hearProcess.stderr.on('data', (data) => {
    const output = data.toString();
    console.error('stderr:', output);
  });

  hearProcess.on('close', (code) => {
    console.log(`online gtasr 子进程退出，退出码 ${code}`);
  });
}

function startTakeOverOnlineHearProcess(req) {
  let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gtasr') : path.join(app.getAppPath(), '../../lib/gtasr');
  let args = ['-l', req.language, '-p', '-w', req.device];
  // let args = ['-l', 'zh-CN', '-p', '-w', 'MacBook Pro麦克风'];
  console.log('Executing command:', pkgpPath, args.join(' '));
  takeOverHearProcess = spawn(pkgpPath, args);
  //开始计时
  restartTime()
  takeOverHearProcess.stdout.on('data', (data) => {
    //停掉识别进程
    if(newProcessIsTakeOver && hearProcess){
        hearProcess.kill('SIGTERM');
        hearProcess = null;
        console.log('kill hearprocess ',hearProcess)
    }
    const output =data.toString()
    if(mainWindow && mainWindow.webContents){
      mainWindow.webContents.send('asrResult', { type: 'speechRecognized', data: output });
    }
  });

  takeOverHearProcess.stderr.on('data', (data) => {
    const output = data.toString();
    console.error('stderr:', output);
  });

  takeOverHearProcess.on('close', (code) => {
    console.log(`take over gtasr 子进程退出，退出码 ${code}`);
  });
}

function startOfflineHearProcess(req) {
  let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gtasr') : path.join(app.getAppPath(), '../../lib/gtasr');
  let args = ['-l', req.language, '-p', '-w', req.device];
  // let args = ['-l', 'zh-CN', '-p', '-w', 'MacBook Pro麦克风'];
  console.log('Executing command:', pkgpPath, args.join(' '));
  hearProcess = spawn(pkgpPath, args);
  hearProcess.stdout.on('data', (data) => {
    const output = JSON.parse(data.toString());
    //判断当前的和上次的长度差，如果超过6个就强制断句，发送上一个的speechRecognized
    if(lastAsrResult){
      let segmentsLength = output.segmentsLength
      if(output.type != "speechRecognized" && lastAsrResult.type != "speechRecognized"  && lastAsrResult.segmentsLength - segmentsLength > 6){
        lastAsrResult.type = 'speechRecognized'
        mainWindow.webContents.send('asrResult', { type: 'speechRecognized', data: JSON.stringify(lastAsrResult) });
        lastAsrResult = null
      }
    }
    lastAsrResult = output
    console.log('stdout:', output);
    mainWindow.webContents.send('asrResult', { type: 'speechRecognized', data: JSON.stringify(output) });
  });

  hearProcess.stderr.on('data', (data) => {
    const output = data.toString();
    console.error('stderr:', output);
  });

  hearProcess.on('close', (code) => {
    console.log(`gtasr 子进程退出，退出码 ${code}`);
  });
}
function stopHearProcess() {
  if (hearProcess) {
    console.log('Stopping hear process');
    hearProcess.kill('SIGTERM');
    hearProcess = null;
  } else {
    console.log('No hear process to stop');
  }
  if (takeOverHearProcess) {
    console.log('Stopping hear takeOverHearProcess process');
    takeOverHearProcess.kill('SIGTERM');
    takeOverHearProcess = null;
  }
}

let mainWindow

function createWindow() {
  /**
   * Initial window options
   */
  mainWindow = new BrowserWindow({
    icon: path.resolve(__dirname, 'icons/icon.png'), // tray icon
    width: 1000,
    height: 600,
    useContentSize: true,
    show: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      // More info: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/electron-preload-script
      preload: path.resolve(__dirname, process.env.QUASAR_ELECTRON_PRELOAD),
    }
  })

  mainWindow.loadURL(process.env.APP_URL)
  if (process.env.DEBUGGING) {
    // if on DEV or Production with debug enabled
    mainWindow.webContents.openDevTools()
  }
  //  else {
  //   // we're on production; no access to devtools pls
  //   mainWindow.webContents.on('devtools-opened', () => {
  //     mainWindow.webContents.closeDevTools()
  //   })
  // }
  mainWindow.setMenu(null)
  mainWindow.on('closed', () => {
    stopVosk()
    stopHearProcess()
    // stopDeeplx()
    quitGtAudio()
    mainWindow = null
  })
  let checkGTA = null;
  mainWindow.on('show', () => {
    if (process.platform == 'darwin') {
      let appName = 'Green Terp Audio'
      if (checkGTA) {
        clearInterval(checkGTA)
      }
      let command = 'ls /Applications/ | grep -i \"Green Terp Audio\"';
      exec(command, async (error, stdout, stderr) => {
        if (stdout) {
          console.log('Green Terp Audio installed ')
          //判断是什么版本
          let version = '1.0'
          let needUpgradeGTA = false
          let nowVersion = await getGTAVersion()
          console.log('nowVersion ', nowVersion)
          if (nowVersion.startsWith(version)) {
            needUpgradeGTA = true
          }
          console.log('needUpgradeGTA ', needUpgradeGTA)
          if (needUpgradeGTA) {
            quitGtAudio()
            installGTA2()
          } else {
            const log = exec('ps -e | grep -v grep | grep "' + appName + '"');
            let is_running = false;
            log.stdout.on('data', () => {
              is_running = true;
            });
            log.stdout.on('end', () => {
              let order = 'open -a \"' + appName + '.app\"'
              exec(order)
            });
          }
        } else {
          // installGTA2()
        }
      });
    }
  })
  function installGTA2() {
    let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/GreenTerpAudio-2.0.pkg') : path.join(app.getAppPath(), '../../lib/GreenTerpAudio-2.0.pkg')
    exec('open ' + pkgpPath)
    console.log('Green Terp Audio not installed')
  }
  var index = 0
  function stopDeeplx() {
    if (deeplxprocess) {
      console.log('Stopping deeplx process');
      deeplxprocess.kill('SIGTERM');
      deeplxprocess = null;
    } else {
      console.log('No deeplx process to stop');
    }
  }
  function startDeeplx() {
    if (deeplxprocess) {
      deeplxprocess.kill('SIGTERM');
      deeplxprocess = null;
    }
    let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/gttranslate') : path.join(app.getAppPath(), '../../lib/gttranslate');
    deeplxprocess = spawn(pkgpPath);
    deeplxprocess.stdout.on('data', (data) => {
      const output = data.toString();

      console.log('stdout:', output);
    });

    deeplxprocess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error('stderr:', output);
    });

    deeplxprocess.on('close', (code) => {
      console.log(`gttranslate 子进程退出，退出码 ${code}`);
      index++
      if (index < 50) {
        startDeeplx()
      }
    });
  }
  mainWindow.on('ready-to-show', function () {
    mainWindow.show() // 初始化后再显示
    setTimeout(() => {
      // startDeeplx()
    }, 2000);
  })

}

// 获取文件夹事件 开始
// 可获取的文件后缀
let watchPath = null
const allowedExtensions = ['docx', 'pptx', 'xlsx', 'odt', 'odp', 'ods', 'pdf'];
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openDirectory'],
  });
  if (result.canceled) return null;
  const folderPath = result.filePaths[0];

  // 监听文件夹变动 开始
  if (watchPath) watchPath.close()
  // 监视指定目录及其子目录中的所有变化
  watchPath = chokidar.watch(folderPath, {
    ignored: /(^|[\/\\])\../,
    persistent: true, // 持续监视
    ignoreInitial: true
  }).on('add', () => {
    const files = [{
      id: 'root',
      name: path.basename(folderPath), // 根目录名称
      path: folderPath,                // 根目录路径
      type: 'directory',               // 标记为目录类型
      children: buildFileTree(folderPath) // 递归获取子文件和文件夹
    }]
    mainWindow.webContents.send('folder-changed', { files });
  }).on('unlink', () => {
    const files = [{
      id: 'root',
      name: path.basename(folderPath), // 根目录名称
      path: folderPath,                // 根目录路径
      type: 'directory',               // 标记为目录类型
      children: buildFileTree(folderPath) // 递归获取子文件和文件夹
    }]
    mainWindow.webContents.send('folder-changed', {files});
  })
  // 监听文件夹变动 结束

  return [{
      id: 'root',
      name: path.basename(folderPath), // 根目录名称
      path: folderPath,                // 根目录路径
      type: 'directory',               // 标记为目录类型
      children: buildFileTree(folderPath) // 递归获取子文件和文件夹
  }]
});
function buildFileTree(dir) {
  const files = fs.readdirSync(dir);
  return files
  .map(file => {
    const filePath = path.join(dir, file);
    const isDirectory = fs.statSync(filePath).isDirectory();
    const ext = path.extname(file).slice(1).toLowerCase(); // 获取文件扩展名（不含点号）
    // 递归处理文件夹，过滤非文件夹的指定后缀文件
    if (isDirectory) {
      return {
        name: file,
        id: filePath,
        path: filePath,
        type: 'directory',
        children: buildFileTree(filePath), // 递归获取子文件夹
      };
    } else if (allowedExtensions.includes(ext)) {
      return {
        id: filePath,
        name: file,
        path: filePath,
        type: 'file',
      };
    }
    return null;
  })
  .filter(item => item !== null); // 过滤掉未包含在允许列表中的文件
}
// 获取文件夹事件 结束

//解析文件内容
ipcMain.handle('read-file',async (event,path) => {
  try {
    return fs.readFileSync(path)
  } catch (err) {
    return false
  }
})
// 删除指定文件
ipcMain.handle('delete-file', async (event, path) => {
  return fs.unlinkSync(path)
})
//写入文件内容
ipcMain.handle('write-file', async (event, path, htmlString) => {
  const fileBuffer = await HTMLtoDOCX(htmlString, null, {
    table: {row: {cantSplit: true}},
    footer: true,
    pageNumber: true,
    preprocessing: {skipHTMLMinify: false}
  });
  return fs.writeFileSync(path, fileBuffer)
})


app.whenReady().then(() => {
  createWindow()
  // reset menu
  const template = [
    {
      label: 'Edit',
      submenu: [{ role: 'undo' }, { role: 'redo' }, { type: 'separator' }, { role: 'cut' }, { role: 'copy' }, { role: 'paste' }, { role: 'pasteandmatchstyle' }, { role: 'delete' }, { role: 'selectall' }]
    }
  ]
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        {
          role: 'quit'
        }
      ]
    })
  }
  const menu = Menu.buildFromTemplate(template)
  // Menu.setApplicationMenu(menu)
})

app.on('window-all-closed', () => {
  if (platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow()
  }
})

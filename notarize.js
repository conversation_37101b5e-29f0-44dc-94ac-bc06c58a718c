const { notarize } = require('@electron/notarize');
 
exports.default = async function notarizing(context) {
  const { electronPlatformName, appOutDir } = context; 
  if (electronPlatformName !== 'darwin') {
    return;
  }
 
  const appName = context.packager.appInfo.productFilename;
  let result = await notarize({
    tool:'notarytool',
    teamId:'9Q5564HS62',
    appBundleId: 'com.greenterp.interpreter',
    appPath: `${appOutDir}/${appName}.app`,
    appleId: '<EMAIL>',
    appleIdPassword: 'kisc-xdtx-namu-yaof',
  })
  console.log('@@ ',result)
  return result;
}

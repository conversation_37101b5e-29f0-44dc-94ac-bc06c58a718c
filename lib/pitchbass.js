const winDriver = 'gtassist.exe';
const macDrive = './gtassist'
const { ipc<PERSON><PERSON><PERSON> } = require('electron')
const { spawn } = require('child_process');
import { changeOutputDevice2Default, changeInputDevice2Default } from '@/utils/denoiser'

let denoiser;
let soxProcess;

function getSox() {
    return ipcRenderer.invoke("getSoxPath", null).then(res => {
        console.log('get sox path ', res)
        denoiser = res;
    });
}

// 确保在使用前调用了 getSox
getSox();

export function stopSoxPitchBass() {
    return new Promise((resolve, reject) => {
        try{
            if (soxProcess) {
                soxProcess.kill();
            }
            resolve()
        }catch(e){
            reject()
        }
        
    })
}

export function stopSoxProcess() {
    return new Promise((resolve, reject) => {
        try{
            if (soxProcess) {
                soxProcess.kill();
            }
            resolve()
        }catch(e){
            reject()
        }
        
    })
}

export function startSoxPitchBass(pitch, bass, input, output) {
    return new Promise((resolve, reject) => {
        if (soxProcess) {
            soxProcess.kill();
        }

        console.log('pitch value ', pitch, ' bass value ', bass);

        const args = [
            '-B',
            '-t', process.platform === 'darwin' ? 'coreaudio' : 'waveaudio', input,
            '-t', process.platform === 'darwin' ? 'coreaudio' : 'waveaudio', output,
            'pitch', pitch,
            'bass', bass
        ];

        console.log('pb order ',args)

        const driver = process.platform === 'darwin' ? macDrive : winDriver;
        const options = { encoding: 'utf-8', cwd: denoiser };

        soxProcess = spawn(driver, args, options);

        soxProcess.stderr.on('data', (data) => {
            console.error(`SoX error: ${data}`);
            resolve('SoX process completed successfully');
        });

        soxProcess.on('close', (code) => {
            if (code !== 0) {
                reject(new Error(`SoX process exited with code ${code}`));
            } else {
                resolve('SoX process completed successfully');
            }
        });

        soxProcess.on('error', (error) => {
            reject(error);
        });
    });
}

export function changeSystemOutputVolume(val, input, output) {
    return new Promise((resolve, reject) => {
        if (soxProcess) {
            soxProcess.kill();
        }

        console.log('val value ', val);

        const args = [
            '-B',
            '-t', process.platform === 'darwin' ? 'coreaudio' : 'waveaudio', input,
            '-t', process.platform === 'darwin' ? 'coreaudio' : 'waveaudio', output,
            'vol', val
        ];

        console.log('pb order ',args)

        const driver = process.platform === 'darwin' ? macDrive : winDriver;
        const options = { encoding: 'utf-8', cwd: denoiser };

        soxProcess = spawn(driver, args, options);

        soxProcess.stderr.on('data', (data) => {
            console.error(`SoX error: ${data}`);
            resolve('SoX process completed successfully');
        });

        soxProcess.on('close', (code) => {
            if (code !== 0) {
                reject(new Error(`SoX process exited with code ${code}`));
            } else {
                resolve('SoX process completed successfully');
            }
        });

        soxProcess.on('error', (error) => {
            reject(error);
        });
    });
}